# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_cart.py
@Description    :
@CreateTime     :  2023/7/24 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/24 17:06
"""

import weeeTest
from weeeTest import weeeConfig


class SellerCart(weeeTest.TestCase):

    # def seller_cart_action(self, headers, product_id: int, qty: int, cms_key: str, refer_type: "seller", seller_id,
    #                        delivery_date):
    # """marketplace web加购"""
    # new_source = {"mod_nm": cms_key, "mod_pos": 2, "sec_nm": int(seller_id), "sec_pos": 0, "prod_pos": 0,
    #               "page_key": "mweb_mkpl_vendor_landing", "referer_page_key": "",
    #               "view_id": "0328a5550e09461eaa13ed60584eda5c"}

    def seller_cart_action(self, headers, product_id: int, qty: int, seller_id,
                           delivery_date, refer_type="seller"):
        """marketplace web加购"""
        data = [
            {
                "product_id": int(product_id),
                "source": f"mkpl_seller_{seller_id}-all",
                "refer_type": refer_type,
                "refer_value": str(seller_id),
                "delivery_date": delivery_date,
                "min_order_quantity": 1,
                "is_pantry": False,
                "is_alcohol": False,
                "item_type": "",
                "is_mkpl": True,
                "positionInfoT2": {
                    "modSecPos": {
                        "mod_nm": "",
                        "mod_pos": 2,
                        "sec_nm": int(seller_id),
                        "sec_pos": 0
                    },
                    "context": {
                        "filter_sub_category": "recommend"
                    },
                    "prodPos": 0
                },
                "ctx": {
                    "filter_sub_category": "recommend"
                },
                "vender_id": int(seller_id),
                "quantity": qty,
                "new_source": ""
                # "new_source": str(new_source)
            }
        ]
        self.put(url='/ec/so/seller/items/v2', headers=headers, json=data)
        return self.response

    def seller_float_cart_action(self, headers, product_id: int, qty: int, seller_id, delivery_date,
                                 refer_type="seller", source_store="cn"):
        """marketplace mobile float加购"""
        data = [
            {
                "product_id": int(product_id),
                "product_key": str(product_id),
                "quantity": qty,
                "refer_type": refer_type,
                "refer_value": str(seller_id),
                "delivery_date": delivery_date,
                "source_store": source_store
            }
        ]
        self.put(url='/ec/so/seller/items/v2', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
