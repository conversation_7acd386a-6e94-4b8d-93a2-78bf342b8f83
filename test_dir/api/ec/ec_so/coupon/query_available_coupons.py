"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  query_available_coupons.py
@Description    :  
@CreateTime     :  2023/7/21 16:42
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/21 16:42
"""
import weeeTest
from weeeTest import weeeConfig


class QueryAvailableCoupons(weeeTest.TestCase):
    """查询可用的优惠券"""
    def coupons_list(self, headers):
        data = None
        self.get(url="/ec/so/coupons/list", headers=headers, params=data)
        return self.response

    def coupons_list_v2(self, headers, cart_domain: str = "normal"):
        data = {"cart_domain": cart_domain}
        self.get(url="/ec/so/coupons/list/v2", headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
