"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  query_ricepo_available_coupons.py
@Description    :  
@CreateTime     :  2023/7/22 20:07
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 20:07
"""

import weeeTest
from weeeTest import weeeConfig


class QueryRicepoAvailableCoupons (weeeTest.TestCase):
    """查询可用的ricepo优惠券"""

    def coupons_ricepo(self, headers):
        """coupons_ricepo"""
        data = None
        self.get(url="/ec/so/coupons/ricepo", headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
