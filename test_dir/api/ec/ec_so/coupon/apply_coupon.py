"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  apply_coupon.py
@Description    :  
@CreateTime     :  2023/7/21 17:16
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/21 17:16
"""
import weeeTest
from weeeTest import weeeConfig


class ApplyCoupon(weeeTest.TestCase):
    """优惠券应用"""

    def coupons_apply(self, headers, cart_domain: str = "grocery",coupon_code: str = "TESTCODE"):
        """非combine checkout的 coupons_apply"""
        data = {
            "cart_domain": cart_domain,
            "coupon_code": coupon_code
        }
        self.post(url="/ec/so/coupons/apply", headers=headers, json=data)
        return self.response

    def coupons_apply_v2(self, headers, cart_domain: str = "grocery", coupon_code: str = "TESTCODE"):
        """H5 coupons_apply"""
        data = {
            "cart_domain": cart_domain,
            "coupon_code": coupon_code
        }
        self.post(url="/ec/so/coupons/apply/v2", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
