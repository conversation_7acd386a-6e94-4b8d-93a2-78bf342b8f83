"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  add_or_edit_user_address.py
@Description    :  
@CreateTime     :  2023/7/21 15:11
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/21 15:11
"""
import random

import weeeTest
from weeeTest import weeeConfig


class AddOrEditUserAddress(weeeTest.TestCase):
    def so_address(self, headers, comment: str = str(random.randint(1000, 2000)), addr_firstname: str = "firstname",
                   addr_lastname: str = "lastname",
                   phone: str = str(random.randint(2000000000, 9999999999)), email: str = "<EMAIL>",
                   addr_address: str = "18607 Bothell Way NE", addr_city: str = "Bothell",
                   addr_state: str = "84", addr_country: int = 2,
                   addr_zipcode: int = 98011, address_id: int = None):
        """ 新增/编辑地址 """
        data = {"addr_firstname": addr_firstname,
                "addr_lastname": addr_lastname,
                "phone": phone,
                "email": email,
                "comment": "This is a test address" + comment,
                "addr_address": addr_address,
                "addr_apt": "",
                "addr_city": addr_city,
                "addr_state": addr_state,
                "addr_zipcode": addr_zipcode,
                "addr_country": addr_country,
                "address_id": address_id  # 修改地址的时候才传
                }
        self.post(url="/ec/so/address", headers=headers, json=data)
        return self.response

    def add_address(self, headers: object, addr_firstname: str, addr_lastname: str, phone: str, comment: str
                    , email: str, addr_address: str, addr_apt: str, addr_city: str, addr_state: str, addr_zipcode: str,
                    force: bool) -> object:
        """ 新增/编辑地址 """
        data = {"addr_firstname": addr_firstname,
                "addr_lastname": addr_lastname,
                "phone": phone,
                "email": email,
                "force": force,
                "comment": comment,
                "addr_address": addr_address,
                "addr_apt": addr_apt,
                "addr_city": addr_city,
                "addr_state": addr_state,
                "addr_zipcode": addr_zipcode,
                "addr_country": 2}
        self.post(url="/ec/so/address", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
