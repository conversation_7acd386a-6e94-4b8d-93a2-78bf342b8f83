# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class CancelOrder(weeeTest.TestCase):

    def cancel_order(self, headers, order_id, confirmed: str = True):
        """#cancel_order"""
        data = {
            "cancel_reason": "测试整单取消",
            "confirmed": confirmed,
            "note": "",
            "order_id": order_id
        }
        self.post(url='/ec/so/order/all/cancel', headers=headers, json=data)
        return self.response

    def partial_cancel_order(self, headers, id, product_id, order_id, quantity: str = "1"):
        """#partial_cancel_order"""
        data = {
            "comment": "测试部分取消",
            "confirmed": True,
            "items": [
                {
                    "id": id,
                    "product_id": product_id,
                    "quantity": quantity
                }
            ],
            "order_id": order_id
        }
        self.put(url='/ec/so/order/partial', headers=headers, json=data)
        return self.response

    def cancel_unpaid_order(self, headers, order_id):
        """#cancel_unpaid_order"""
        data = None
        self.put(url='/ec/so/order/unpaid/' + order_id, headers=headers, json=data)
        return self.response

    def cancel_unpaid_order_new(self, headers, order_ids: list):
        self.post(url='/ec/so/order/unpaid/cancel', headers=headers, json=order_ids)
        return self.response

    def cancel_order_tip(self, headers, order_id):
        """#cancel_order_tip"""
        data = None
        self.put(url='/ec/so/order/tip/' + order_id, headers=headers, json=data)
        return self.response

    def cancel_request(self, headers, uuid, cancel_type, order_id):
        data = {
            "uuid": uuid,
            "cancel_type": cancel_type,
            "order_id": order_id
        }
        self.post(url='/ec/so/order/cache/cancel_request', headers=headers, json=data)
        return self.response

    def refund_points_flag(self, headers):
        data = None
        self.get(url='/ec/so/config/refund_points_flag/value', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
