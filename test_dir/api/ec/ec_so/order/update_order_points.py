# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class UpdateOrderPoints(weeeTest.TestCase):

    def update_order_points(self, headers, points_b: str = "500",
                            points_price: str = "5", purchase_id: str = "1"):
        """#update_order_points"""
        data = {
            "points_b": points_b,
            "points_price": points_price,
            "purchase_id": purchase_id
        }
        self.post(url='/ec/so/order/points/update', headers=headers, json=data)
        return self.response

    def batch_update_order_points(self, headers, points_b: str = "500",
                                  points_price: str = "5", purchase_id: str = "1"):
        """#batch_update_order_points"""
        data = [
            {
                "points_b": points_b,
                "points_price": points_price,
                "purchase_id": purchase_id
            }
        ]
        self.post(url='/ec/so/order/points/update/batch', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
