import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath

class SellerInfo(weeeTest.TestCase):

    def vendor_seller_info(self, headers, zipcode=None, seller_ids=[]):

        """
        提供外部服务调用的api，比如Global+ 商品PDP页面
        @param headers:
        @param zipcode:
        @param seller_ids:
        @return:
        """
        data = {
            "zipcode": zipcode,
            "seller_ids": seller_ids
        }

        self.post(url='/ec/marketplace/vendor/seller/info', headers=headers, json=data)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'






