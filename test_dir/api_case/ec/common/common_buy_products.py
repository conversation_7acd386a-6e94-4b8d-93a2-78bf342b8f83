import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order.upshell import Upshell
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart


class CommonBuy(weeeTest.TestCase):
    @staticmethod
    def buy_upsell_product(header, cart_domain="grocery"):
        """
        buy upsell products
        """
        upsell_result = Upshell().upsell_v2(headers=header, cart_domain='grocery')
        upsell_product_list = upsell_result['object']['upsell_list']

        if upsell_product_list:
            upsell_product_ids = [item for upsell in upsell_product_list for item in upsell['items'] if upsell['items'] and item['sold_status'] == 'available']
            if upsell_product_ids:
                # 购买upsell商品
                for index, product in enumerate(upsell_product_ids):
                    add_upsell = UpdatePreOrderLine().porder_items_cart_upsell(
                        headers=header,
                        product_id=product['id'],
                        quantity=product['min_order_quantity']
                    )
                    assert add_upsell["result"] is True and add_upsell["object"][
                        "updateItems"], f"product is {product}"
                    CommCheckProductsWithCart().check_product_exists_in_cart(
                        headers=header,
                        cart_domain=cart_domain,
                        product_id=product['id']
                    )
                    if index == 3:
                        break



        else:
            log.info("本次结算没有Upsell商品")
