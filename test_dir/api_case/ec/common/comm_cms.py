"""
<AUTHOR>  suqin
@Version        :  V1.0.0
------------------------------------
@File           :  RemoveAllProductsInCart.py.py
@Description    :
@CreateTime     :  2023/8/12 17:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/12 17:40
"""
import json
import time
from typing import Any

import requests
import weeeTest
import logging
import random

from test_dir.api.ec.ec_content.cms.cms_sayweee_mobile_home import CmsSayweeeHome
from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_home import DsCollectionHome
from test_dir.api.ec.ec_item.my_favorite.api_favorites import ApiFavorites
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent


class CommCheckFunction(weeeTest.TestCase):
    def comm_cms_component(self, headers, component_key, component_instance_key, scenes):
        # 获取首页所有组件数据
        cms_data = CmsSayweeeHome().cms_sayweee_mobile_home(headers=headers, zipcode=95008, sales_org_id=1)
        assert cms_data["result"] is True
        layout = cms_data["object"]["layout"]
        components = cms_data["object"]["layout"]["sections"][0]["components"]
        cms_collection = None
        for component in components:
            # print(component)
            try:
                if component["component_key"] == component_key and component[
                    "component_instance_key"] == component_instance_key:
                    component_instance_key = component["component_instance_key"]
                    dataobject_key = component["datasource"][0]

                    # 根据组件key，获取首页组件

                    cms_collection = DsCollectionHome().ds_collection_home(headers=headers,
                                                                           ds_url=component_instance_key,
                                                                           dataobject_key=dataobject_key,
                                                                           scenes=scenes)
                    return cms_collection["object"]


            except Exception as e:
                logging.info("组件程序执行失败，请确认" + str(e))
        # 说明这个组件不存在，返回特殊值
        if cms_collection is None:
            cms_collection = "90000"
            return cms_collection

    def comm_check_pdp_link(self, product_id, view_link, headers):
        # 验证商品pdp链接
        assert str(product_id) in view_link, f'这个商品{product_id}pdp链接{view_link}返回的不是这个产品的链接，请确认~'
        response = self.get(url="", special_url=view_link, headers=headers)
        try:
            assert response.status_code == 200, f"status_code={response.status_code}, response={response.text}, view_link={view_link}"
            assert str(
                product_id) in response.text, f'这个pdp链接{view_link}返回的不是这个产品的链接，请确认~,返回结果为：{response}'
        except Exception as e:
            time.sleep(3)
            response = self.get(url="", special_url=view_link, headers=headers)
            assert response.status_code == 200, f"status_code={response.status_code}, response={response.text}, view_link={view_link}"
            assert str(
                product_id) in response.text, f'这个pdp链接{view_link}返回的不是这个产品的链接，请确认~,返回结果为：{response}'

    def comm_check_link(self, view_link, headers):
        # 验证接口返回url
        response = self.get(url="", special_url=view_link, headers=headers, allow_redirects=False)
        try:
            assert response.status_code in [200,
                                            302, 307], f"这个链接{view_link}访问异常，请确认,返回结果为：{response.text}, code={response.status_code}"
        except Exception as e:
            time.sleep(10)
            response = self.get(url="", special_url=view_link, headers=headers, allow_redirects=False)
            assert response.status_code in [200,
                                            302, 307], f"这个链接{view_link}访问异常，请确认,返回结果为：{response.text}, code={response.status_code}, exception={str(e)}"

    def comm_set_favorites(self, headers, target_id):
        # 点击搜藏
        favorites_set = ApiFavorites().favorites_set(headers=headers, target_id=target_id)
        favorite_list = ApiFavorites().my_favorite_product_list_v2(headers=headers)
        # 获取所有产品的ID列表
        product_ids = [product["id"] for product in favorite_list["object"]["products"]]
        if favorites_set["object"] is True:
            # 判断搜藏成功
            assert target_id in product_ids, f"商品 {target_id} 没有搜藏列表里，请确认~."
            #
            # assert favorite_list["object"]["products"][0][
            #            "id"] == target_id, f"商品 {target_id} 没有搜藏列表里，请确认~."
        else:
            # 获取所有产品的ID列表
            product_ids = [product["id"] for product in favorite_list["object"]["products"]]
            # 判断取消搜藏成功
            assert target_id not in product_ids, f"商品 {target_id} 还在搜藏列表里，没有取消成功，请确认~."

    def comm_catalogue_content(self, headers, date, zipcode, filter_sub_category,
                               filters: str = None, sort: str = "recommend"):
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=headers, date=date,
            zipcode=zipcode, filter_sub_category=filter_sub_category,
            sort=sort, filters=filters
        )
        assert catalogue_content.get('object').get(
            'contents'), f'category为{filter_sub_category}过滤 {filters}没有商品{catalogue_content}，请检查环境'
        return catalogue_content
