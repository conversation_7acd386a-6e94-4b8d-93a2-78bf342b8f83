from weeeTest.testdata.common.mysql.ext.mysqlUtil import MysqlUtil

from qa_config.secret import get_secret
import asyncio
import os


class TaxInfo:
    def __init__(self, db='weee_comm'):
        self.db_config = get_secret()
        self.db = db
        self.mysql = MysqlUtil(host='weee.db.tb1.sayweee.net', user=self.db_config['db_erp_username'],
                               password=self.db_config['db_erp_password'], db=self.db)

    async def get_tax_from_gb_order_product(self, sql):
        with self.mysql as conn:
            res_query = await conn.execute_query(sql_str=sql)
            print("res===>", res_query)
            return res_query


if __name__ == '__main__':
    j = asyncio.run(TaxInfo().get_tax_from_gb_order_product(f"select * from weee_comm.gb_order_product limit 5"))
    print(j)
    [print(p) for i in j for p in i]
