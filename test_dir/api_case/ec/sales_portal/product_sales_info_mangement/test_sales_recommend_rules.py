# !/usr/bin/python3
# -*- coding: utf-8 -*-
import pytest
import weeeTest

from test_dir.api.ec.central_portal.central_recommend import CentralRecommend


class TestSalesRecommendRules(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_recommend_rules')
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_recommend_rules(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-商品推荐规则-查询商品推荐规则页面数据"""
        flow = ["retrieval", "ranking", "re-ranking"]
        for flow in flow:
            recommend_trace_product_list = CentralRecommend().recommend_trace_product_list(headers=sales_header,
                                                                                           flow=flow)

            assert len(recommend_trace_product_list[
                           "object"]) > 0, f'查询商品推荐规则页面数据异常{recommend_trace_product_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_recommend_rules_with_all_params')
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_recommend_rules_with_all_params(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-商品推荐规则-传入所有参数测试"""
        from datetime import datetime, timedelta

        # 测试参数配置
        test_params = {
            "date": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"),
            "experiment": "test_experiment",
            "experiment_id": "exp_001",
            "experiment_group": "group_a",
            "flow": "re-ranking",
            "flow_id": "flow_001",
            "lang": "en",
            "pageSize": 10,
            "platform": "h5",
            "sales_org_id": 4,
            "scenes_id": 3,
            "startColumn": 0,
            "store": "cn",
            "zipcode": "98011"
        }

        recommend_trace_product_list = CentralRecommend().recommend_trace_product_list(
            headers=sales_header,
            date=test_params["date"],
            experiment=test_params["experiment"],
            experiment_id=test_params["experiment_id"],
            experiment_group=test_params["experiment_group"],
            flow=test_params["flow"],
            flow_id=test_params["flow_id"],
            lang=test_params["lang"],
            pageSize=test_params["pageSize"],
            platform=test_params["platform"],
            sales_org_id=test_params["sales_org_id"],
            startColumn=test_params["startColumn"],
            store=test_params["store"],
            zipcode=test_params["zipcode"]
        )

        # 断言接口返回成功
        assert recommend_trace_product_list["result"] is True, f'接口调用失败: {recommend_trace_product_list}'
        # 断言返回数据不为空
        assert len(recommend_trace_product_list["object"]) > 0, f'查询商品推荐规则页面数据异常{recommend_trace_product_list}'
