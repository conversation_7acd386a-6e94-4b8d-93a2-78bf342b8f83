"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_grocery_orderxunhuan.py
@Description    :  
@CreateTime     :  2023/8/31 17:49
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/31 17:49
"""
import weeeTest

from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class TestGroceryOrderXunhuan(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    def test_grocery_orderxunhuan(self, *args, ec_zhuli_header):
        # 结算暂时不迁到线上
        # 批量下单（暂时循环下2单）
        i = 0
        for i in range(2):
            """订单-对生鲜进行订单"""
            # # 获取用户的preorder
            # QuerySimplePreOrder().query_simple_preorder_v2(headers=headers)["object"]
            # 查询购物车v3
            QueryPreOrder().query_preorder_v3(headers=ec_zhuli_header)
            # 清空购物车所有的商品
            # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
            # 加购商品到购物车
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=args[0]["cart"]["product_id"],
                                                 quantity=args[0]["cart"]["quantity"])
            pre = PrepareCheckout().prepare_checkout_v2(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"], index=args[0]["order"]["index"], tip=args[0]["order"]["tip"])
            # 获取地址列表数据
            address_list = QueryUserAddressList().address_list(headers=ec_zhuli_header)
            address_id = address_list["object"][0]["id"]
            ApplyUserAddressInformation().address_apply_v2(headers=ec_zhuli_header, address_id=address_id)
            checkoutamount = pre["object"]["fee_info"]["final_amount"]
            checkout_pre_id = pre["object"]["checkout_pre_id"]
            """获取第一个profile_id值"""
            profile_id = PaymentCategory().braintree_profiles(headers=ec_zhuli_header)["object"][0]['profile_id']
            # 使用信用卡支付
            PaymentCategory().payment_category(headers=ec_zhuli_header, payment_category=args[0]["order"]["payment_category"],
                                               profile_id=profile_id, points=True)
            print("订单号", Checkout().checkout_v3(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                                   checkout_pre_id=checkout_pre_id, checkoutAmount=checkoutamount)["object"]["order_ids"])
            i += 1
            print("第", i, "单下单完成")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
