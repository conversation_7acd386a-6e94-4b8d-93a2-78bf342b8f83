"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_grocery_order.py
@Description    :  
@CreateTime     :  2023/8/31 17:49
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/31 17:49
"""
import json

import weeeTest

from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api_case.ec.common.generate_order import CommonOrder

class TestGroceryOrder(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'tb1')
    def test_grocery_order(self, ec_zhuli_header):
        """订单-对生鲜进行下单"""
        # 此订单由【积分支付】，所以只能在tb1进行, tb1不太稳定，不能保证每次都能拿到订单
        address_list = QueryUserAddressList().address_list(ec_zhuli_header)
        address_ids = [address["id"] for address in address_list["object"]]
        print("address_ids", address_ids[0])
        address_id = address_ids[0]
        assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        order = CommonOrder().get_order_id(ec_zhuli_header, address_id)
        print("生鲜订单号", order['order_ids'])
        assert order['order_ids'], f'订单生成失败，接口返回数据为：{order}'

        # 整单取消订单第一个订单
        cancel_order = CancelOrder().cancel_order(headers=ec_zhuli_header, order_id=order['order_ids'][0])
        assert cancel_order['object']['error_type'], f'订单取消失败，接口返回数据为：{cancel_order}'
        # 获取取消订单的信息
        # GetCancelOrderReason().get_cancel_order_reason(headers=headers)
        # CancelOrder().refund_points_flag(headers=headers)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
