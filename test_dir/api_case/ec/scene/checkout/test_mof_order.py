"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_mof_order.py
@Description    :  
@CreateTime     :  2023/11/7 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/7 10:30
"""
import weeeTest

from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.order_query.get_modify_order_info import GetModifyOrderInfo
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_zipcode_by_region_id import QueryZipcodeByRegionId
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class TestMofOrder(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction','tb1')
    def test_mof_order(self, *args, ec_zhuli_header):
        """订单-下MOF地区订单(加购/结算/部分修改订单/取消订单)"""
        # 结算暂时不迁到线上
        # 获取用户的preorder
        # 设置zipcode为20662
        QueryZipcodeByRegionId().query_zipcode_by_region_id(headers=ec_zhuli_header, zipcode=args[0]["preorder"]["MOFzipcode"])
        # 加购商品后收取9.99的冷链费
        UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=args[0]["cart"]["mof_product_id"],
                                             quantity=2)
        cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
        print("购物车加购MOF，冷链费信息", cart["object"]["sections"][0]["shipping_info"]["cold_package_fee"])

        pre = PrepareCheckout().prepare_checkout_v2(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"])
        # 获取地址列表数据
        # address_list = QueryUserAddressList().address_list(headers=headers)
        # address_id = address_list["object"][0]["id"]
        ApplyUserAddressInformation().address_apply_v2(headers=ec_zhuli_header, address_id=6442)
        checkoutamount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        """获取第一个profile_id值"""
        profile_id = PaymentCategory().braintree_profiles(headers=ec_zhuli_header)["object"][0]['profile_id']
        # 使用积分支付
        PaymentCategory().payment_category(headers=ec_zhuli_header, payment_category=args[0]["order"]["payment_category"],
                                           profile_id=profile_id, points=True)
        order_id = Checkout().checkout_v3(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                          checkout_pre_id=checkout_pre_id, checkoutAmount=checkoutamount)["object"][
            "order_ids"]
        print("MOF订单号：", order_id)
        # 获取下的订单的第一个订单值
        order_id1 = order_id[0]
        # 查询是否需要整单取消
        modify_order_info = GetModifyOrderInfo().get_modify_order_info(headers=ec_zhuli_header, order_id=order_id1)
        product_id = modify_order_info["object"]["products"][0]["id"]
        # print("订单号", order_id1)
        # 部分修改订单
        print("部分修改订单成功",
              CancelOrder().partial_cancel_order(headers=ec_zhuli_header, product_id=args[0]["cart"]["mof_product_id"],
                                                 order_id=order_id1, quantity="1", id=product_id)["object"]["error_type"])
        # 整单取消订单第一个订单
        print("整单取消成功", CancelOrder().cancel_order(headers=ec_zhuli_header, order_id=order_id1)["object"]["error_type"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
