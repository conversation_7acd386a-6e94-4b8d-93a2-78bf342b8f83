"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_top_message.py
@Description    :  
@CreateTime     :  2023/12/1 10:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/12/1 10:01
"""
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_customer.message.message_home_header import MessageHomeHeader
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestTopMessage(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction',"skip",'tb1')
    def test_top_message(self, *args, ec_login_header):
        """消息-验证合单提醒展示"""
        # 结算暂时不迁到线上
        # 获取用户的preorder

        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_login_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)
        assert self.response["result"] is True
        porder = self.response["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 通过分类接口获取生鲜商品1
        normal = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_login_header, zipcode=zipcode,
                                                                        date=deal_date, filter_sub_category=
                                                                        "fruits")
        itmes = jmespath(normal, "object.contents")
        product_list = []
        for item in itmes:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item["type"] != "carousel" and item["data"]["sold_status"] != "change_other_day":
                product_list.append(item["data"]["id"])
        if not product_list:
            print("没有可售的生鲜商品")
            UpdatePreOrderLine().porder_items_v2(headers=ec_login_header, product_id=args[0]["cart"]["product_id"],
                                                 quantity=args[0]["cart"]["quantity"])
        else:
            # 加购水果分类返回的第一个有效商品
            UpdatePreOrderLine().porder_items_v2(headers=ec_login_header, product_id=product_list[0],
                                                 quantity=args[0]["cart"]["quantity"])

        pre = PrepareCheckout().prepare_checkout_v2(headers=ec_login_header, cart_domain=args[0]["cart"]["cart_domain"],
                                                    index=args[0]["order"]["index"], tip=args[0]["order"]["tip"])
        # 获取地址列表数据
        address_list = QueryUserAddressList().address_list(headers=ec_login_header)
        address_id = address_list["object"][0]["id"]
        ApplyUserAddressInformation().address_apply_v2(headers=ec_login_header, address_id=6198)
        checkoutamount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        """获取第一个profile_id值"""
        profile_id = PaymentCategory().braintree_profiles(headers=ec_login_header)["object"][0]['profile_id']
        # 使用信用卡支付
        PaymentCategory().payment_category(headers=ec_login_header, payment_category=args[0]["order"]["payment_category"],
                                           profile_id=profile_id, points=True)
        order_id = Checkout().checkout_v3(headers=ec_login_header, cart_domain=args[0]["cart"]["cart_domain"],
                                          checkout_pre_id=checkout_pre_id, checkoutAmount=checkoutamount)["object"][
            "order_ids"]
        print("生鲜订单号：", order_id)
        topmessage = MessageHomeHeader().message_home_header(headers=ec_login_header, version="v2", lang="zh", date=deal_date)
        print("topmessage", topmessage)
        # 获取首页的top message数据
        # print("获取首页合单提醒文案",
        #       MessageHomeHeader().message_home_header(headers=headers, version="v2", lang="zh",
        #                                               date=preorder["deal_date"])["object"][0]["message"]["short_message"])


