"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_pantry_order.py
@Description    :  
@CreateTime     :  2023/9/13 13:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/13 13:04
"""
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class TestPantryOrder(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('tb1')
    def test_pantry_order(self, *args, ec_zhuli_header):
        """订单-对pantry进行订单"""

        # 结算暂时不迁到线上
        # 获取用户的preorder
        proder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)["object"]
        # 查询购物车v3
        QueryPreOrder().query_preorder_v3(headers=ec_zhuli_header)
        # 清空购物车所有的商品
        # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
        # 筛选特价精选的pantry商品
        pantry = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_zhuli_header, zipcode=proder["zipcode"],
                                                                        date=proder["deal_date"], filter_sub_category=
                                                                        args[0]["category"]["filter_sub_category"][2],
                                                                        filters=args[0]["category"]["filters"])

        assert pantry["object"]["contents"] is not None, f"没有符合条件的商品，条件为：filter_sub_category={args[0]['category']['filter_sub_category'][2]} and filters={args[0]['category']['filters']}"
        UpdatePreOrderLine().porder_items_v2(headers=ec_zhuli_header, product_id=args[0]["cart"]["pantry_productid"],
                                             quantity=args[0]["cart"]["quantity"])

        # 通过分类获取 onsale的第一个pantry商品
        # pantry_id = pantry["object"]["contents"][1]["data"]["id"]
        # print(pantry_id)
        # 加购筛选的第一个pantry商品
        UpdatePreOrderLine().porder_items_v2(headers=ec_zhuli_header, product_id=pantry[0],
                                             quantity=args[0]["cart"]["quantity"])
        # 应用地址信息
        ApplyUserAddressInformation().address_apply_v2(headers=ec_zhuli_header, address_id=6198)
        pre = PrepareCheckout().prepare_checkout_v2(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"])
        checkoutamount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        """获取第一个profile_id值"""
        profile_id = PaymentCategory().braintree_profiles(headers=ec_zhuli_header)["object"][0]['profile_id']
        print(profile_id)
        # 使用信用卡支付
        PaymentCategory().payment_category(headers=ec_zhuli_header, payment_category=args[0]["order"]["payment_category"],
                                           profile_id=profile_id, points=True)
        # 结算
        print("pantry订单号", Checkout().checkout_v3(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                                     checkout_pre_id=checkout_pre_id, checkoutAmount=checkoutamount)[
            "object"]["order_ids"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
