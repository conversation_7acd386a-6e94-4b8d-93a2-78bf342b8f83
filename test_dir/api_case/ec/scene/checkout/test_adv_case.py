"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_adv_case.py
@Description    :  
@CreateTime     :  2023/11/18 20:24
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/18 20:24
"""
import weeeTest

from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.order_query.order_success_page_information import OrderSuccessPageInformation
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestAdvCase(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Regression-skip', 'tb1')
    def test_adv_case(self, *args, ec_zhuli_header):
        """订单-订单成功页adv，目前实验已关闭走了对照组"""
        # 获取登录header
        # skip用例暂时不迁
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        porder = self.response["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 加购生鲜商品
        UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=args[0]["cart"]["product_id"],
                                             quantity=args[0]["cart"]["quantity"], source=args[0]["cart"]["source_pdp"])
        address_list = QueryUserAddressList().address_list(headers=ec_zhuli_header)['object']
        result = [(x["id"]) for x in address_list]
        address_id = result[0]
        ApplyUserAddressInformation().address_apply_v2(headers=ec_zhuli_header, address_id=address_id)
        pre = PrepareCheckout().prepare_checkout_v2(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"])
        checkoutamount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        # """获取第一个profile_id值"""
        profile_id = PaymentCategory().braintree_profiles(headers=ec_zhuli_header)["object"][0]['profile_id']
        # print(profile_id)
        # 使用积分支付
        PaymentCategory().payment_category(headers=ec_zhuli_header,
                                           payment_category=args[0]["order"]["payment_category"],
                                           points=True)
        # 结算2
        # print("一起结算订单号", Checkout().checkout_v3(headers=headers, cart_domain=args[0]["cart"]["cart_domain"],
        #                                                checkout_pre_id=checkout_pre_id, checkoutAmount=checkoutamount)[
        #     "object"]["order_ids"])
        order_id = Checkout().checkout_v3(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                          checkout_pre_id=checkout_pre_id, checkoutAmount=checkoutamount)[
            "object"]["order_ids"]
        print(order_id[0])
        # 获取订单的商品id
        product_list = \
            OrderSuccessPageInformation().order_success_page_information_v2(headers=ec_zhuli_header,
                                                                            checkout_id=order_id[0])[
                "object"]["order_infos"]["product_ids"]
        print("获取订单的商品id", product_list)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
