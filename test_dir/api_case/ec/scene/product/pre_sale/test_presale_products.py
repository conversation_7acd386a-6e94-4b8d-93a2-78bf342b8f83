import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail


# class TestPresaleProducts(weeeTest.TestCase):
#
#     def test_presale_product_card(self, header_for_99991):
#         """
#         【112125】 预售商品卡片
#         """
#         detail = PdpDetail().pdp_detail(
#             **{
#                 "product_id": 96625,
#                 "headers": header_for_99991,
#                 "sales_org_id": None,
#                 "zipcode": "99991",
#                 "category": None
#             }
#         )
#
#         assert jmespath(detail, 'object.product.sales_org_id') == 301
#         assert jmespath(detail, 'object.product.is_presale') is True
#         info_html = jmespath(detail, 'object.product.barInfoModules[*].info_html')
#         # 断言Pre-sale标签存在
#         assert any(["Pre-sale" in item for item in info_html])
#         # 断言配送时间展示
#         assert any(["ETA:" in item for item in info_html])
