# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_brand import SearchByBrand
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestGroceryNewBrand(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('invalidate', 'Transaction')
    def test_grocery_new_brand(self, *args, ec_login_header):
        # todo @suqin: 39行分支未到达
        """ 品牌-新品牌实验验证流程 """
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 清除生鲜购物车
        # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
        product_id = args[0]["product_id"]["product_id"]
        # 返访问首页品牌列表
        SearchByBrand().recommend_card_brand_list(headers=ec_login_header,
                                                  dataobject_key="ds_card_line_1008260")
        assert self.response["result"] is True
        try:
            if self.response["object"] is not None:
                card_list = self.response["object"]["card_list"]
                for card in card_list:
                    card_key = card["card_key"]
                    card_type = card["card_type"]
                    assert card_type == "brand_card"
                    more_link = card["more_link"]
                    # 根据指定品牌访问详情页面
                    SearchByBrand().recommend_brand_product_list(headers=ec_login_header,
                                                                 exclude_brand_key=card_key)
                    try:
                        assert self.response["result"] is True
                        # 加购品牌商品
                        # 切换品牌分类
                        SearchByBrand().search_by_brand(headers=ec_login_header, filterBrand=card_key)
                        assert self.response["result"] is True
                        # 分享品牌
                    except AssertionError as error:
                        raise error
                    # 根据指定品牌获取详情页面其他品牌
                    SearchByBrand().recommend_card_brand_list(headers=ec_login_header,
                                                              dataobject_key="ds_card_line_1008260",
                                                              exclude_brand_key=card_key)
                    try:
                        assert self.response["result"] is True
                    except AssertionError as error:
                        raise error
        except AssertionError as error:
            raise error
        # 访问全部品牌落地页
        SearchByBrand().recommend_brand_list(headers=ec_login_header)
        try:
            assert self.response["result"] is True
        except AssertionError as error:
            raise error

