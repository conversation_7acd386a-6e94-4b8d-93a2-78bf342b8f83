# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
import random


from test_dir.api.ec.ec_customer.account_rest.account_event import AccountEvent
from test_dir.api.ec.ec_customer.signup_rest.email_sigup import EmailSignup


class TestEmailSignUpSimple(weeeTest.TestCase):

    # 通过数据库参数化执行

    @weeeTest.mark.list('Regression-skip', 'Social', "Transaction", "tb1")
    @weeeTest.mark.skip("注册现在未完成")
    def test_email_signup_by_email(self, ec_anony_header):
        """注册-邮箱注册流程验证-非邀请好友方式"""
        # 注册用户用你不迁移到线上
        # EmailSignup().email_signup(headers=RequestHeader.ec_anony_header)
        # assert self.response["result"] is True
        # 1. first check email
        AccountEvent().first_time_check_email(
            headers=ec_anony_header,
            value="<EMAIL>"
        )
        print(2)


        # 1. 获取注册key
        signup_web = EmailSignup().email_signup_web_v1(
            headers=ec_anony_header,
            data={
                "email": "<EMAIL>",
                "from": "first_time_use",
                "timestamp": **********,
                "source": "newFlow",
                "referral_id": 0,
                # "recaptcha_response": "03AFcWeA4iDmzaQoKa4MLh6D1MgDVwI53C8kJOhx9TzgF3IcNsPdZyQJ5RQfNfaLSabHuM8KPQJB4ag8RvRlfASM9fZf-8JN7QrVQzmzpl7rJNgYPA4YoeIiaPJTsctC6X-pbfUOMMJbZIyDr6cKJKv9CK10HMji0cghM5gDDia5twTODITDA6vBQd32ZT2tIvLNBSRdlgVDqNh3tcSpW_Ie99i3TAifdVhkwAJKKogaLHRM2iTzpPqEo6PEiOBNp5IJW41t602maC6HYMmKAhyXFo-3fcexa_rZ4drmWEQCgKrMiSTHs2OOnArlwgM6l9Wfo9gZuHswNndf_fdrwZOoX_gwThW6PQwlwbaLUiop9x62nJ-K3OnDHuvwDpBPxkLoGx_DsMabyCsADcZrT-VAWSb3L0p1xQEXxnI7DEdyUj4g8EScXd-DAn6tzy2yso9Nc9WZ3rVHEOCAHaowG65BnLzaZtBmJiJE-wXlV-M4zVeKezojdO-RG22GQML8rnFl3RmA-F0dmLoklR3Vxul9R9wFzIxtz9z4F2a1FDseysTdrthOQRfc9BbC-Luu37LVFHd_z9DcsDepYNDxOQy0YcvTflaHgJdHjnc69VpL4I-_1ac6FwgMzZbCvhlEzvsiiq9akQ9pt5V_hB0822XslcXEhx4EnjNS5THbAZjEwr5OoS_dnNfQkSqq02MXi6Pd6TTtLjXsgoWDg9J88qoySFkpvHJwKhBLdZbAXkXmyCzMYntHjUuKT0JFcuFN4JwG-ERFq6BVzzBwwnq-uRnqYo00hWibUxxaXy0oMkU_pLG8SXuQcNKRIEZ_evT49V9x1B-R_KI_PfdC-39SFKFcmVCFFOCMDVqbDEjzWnsC6MVd2KFGgGUe8BFHXOkdrzeaD2wVoUVzFOb7H74SxiQfWg9oBXpoifVwvQlWM8Yy6CkCJJGMPNzYcQNAXBNLunH97P1x6apdzWraa2N2jMHwbpEF0fbS979YVscy81ppcc5wVjnOlZ8uJIeCgNwegVL5n3r26ucmX0_yiPM29xHEdNerUDz3HMB6QtpOycz0XlxmOAO2j_XhQXIkGPVBPRBLkwoN24lyU9IVnvYHDP7lEPZcsmdHNxxb-P5DuOdEU9udck9mas0vEvuOS2FTHRbvs2WObbS7Vkjbuq_on-izGUA_dagm8fmYqErsF_urwSRb1hq-zrHIiCWHDoJqAUwBdkosj34Zx6eekoSQddtybRmF-IOgyk9RG1HRBmMu2270SfRbiLbD5VgoIsU5XWYy2VLofjRc6r9BZcbObM2-xudN40bEF0C4Kbj-pdvd3jQtc-3bUkKv6fEmYJ1QKoH35-R2p2IWkPelkH_6wHraRIz2xR2HWpRVXvn9CytFMeUxjfGnWP5PUJDXIxoK26QdRYVKc_bh5pYdZ7YAaNnfbY9vE_Wwp7O3JAXsiDLZRb10r9FrxaRwkW9Uj-CgK0LFkAtyv61Em0dAmqdAxXeCYSJQ2iMQlZBMr8LNvZpDf9aA-T5kFUUrLsClhyL66fh6F-qfL426LNIs5G1LoOUjMPb2grzFaU1cu3Z0b9-vgoZHNqUqHKjggkMV3me3jILCrn7djYzHIOrAW1_CorYFRpV-Rk_5twNYxSTZTZx5NTB5ko5JzxF1vMxDGA7J936GRqHJd5n0eb1QLiMYTWwQlNZSNZh8TLglJjsQ9Zqy63MfO4XdvlwcMJ1W-aBxinqmVazBCLSbdmgNwOs4RTBv23x80kpl0CsY6qn_IWBzg42Bk2WVuDk8r_ziQqMwvhOZDNggY6OCvaGPWM1lyac53gaOZmzdjR6C4YFPPtJYMzRQLV6lyVPkbC04TMeERTbpPy3h-snwu-xFi9RuBL3OVRBhy06Vbitnh-kuJaW-0tZOgk2NMbWIC0r42zTp5Ad9RQMnFva2_IzZuG054_N6JbGf_hcb_jl5BTll_7LdI6hNEAohyphBXz8l1E0nkKX-M09kdJ7gj3",
                "ep_partner": ""
            }
        )


    @weeeTest.data.file(file_name='ec_customer_data.json', return_type='dict')
    @weeeTest.mark.list('Social', "Transaction", "tb1")
    def test_email_signup_by_email_ref(self, *args, ec_anony_header):
        """注册-邮箱注册流程验证-邀请好友方式"""
        # 注册用户用你不迁移到线上
        EmailSignup().email_signup(headers=ec_anony_header,
                                   email=f'{random.randint(10 ** 9, (10 ** 10) - 1)}@reftest.com',
                                   referral_id=args[0]["login"]["referral_id"])
        assert self.response["result"] is True

    @weeeTest.mark.list('Social', "Transaction", "tb1")
    def test_email_signup_by_email_v1(self, ec_anony_header):
        """注册-邮箱注册流程验证v1-非邀请好友方式"""
        # 注册用户用你不迁移到线上
        EmailSignup().email_signup_v1(headers=ec_anony_header)
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
