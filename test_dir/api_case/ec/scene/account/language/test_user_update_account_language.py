# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import pytest
import weeeTest
from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest


class TestUpdateAccountLanguage(weeeTest.TestCase):
    @pytest.mark.parametrize("lang", ['zh','ja','ko','vi','zh-Hant','en'])
    @weeeTest.mark.list('Regression', 'Social', 'product')
    def test_user_update_account_lang(self,lang, ec_jiufen_header):
        result = LanguageRest().user_update_account_lang(lang=lang, headers=ec_jiufen_header)
        assert result["result"] is True, f"result is {result['result']}"












