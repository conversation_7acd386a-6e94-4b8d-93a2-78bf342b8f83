# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/4/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_growth_php.weeebates import Weeebates


class TestMkplWeeeebates(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'tb1')
    def test_mkpl_weeebate_create(self, *args, ec_login_header):
        """创建mkpl砍单场景验证"""
        # 获取砍单list并提取出第一个未砍单的mkpl订单
        weeelist = Weeebates().weeebates_list(headers=ec_login_header)
        # print(weeelist)
        data1 = weeelist["object"]["shareOrdersInfo"]
        orderId1 = []
        for share_order in data1:
            if share_order["status"] == "C" and share_order["order_type"] == "seller":
                orderId1.append(share_order["order_id"])
                break
        assert orderId1, "you haven't had mkpl order to share"
        orderId = str(orderId1[0])
        print(orderId)
        orderIds = []
        productIds = []
        for item in data1:
            if str(item["order_id"]) == orderId:
                for order in item["orders"]:
                    orderIds.append(order["order_ids"])
                    productIds.append(order["products"][0]["id"])
        # print("===>", orderIds)
        # print(productIds)
        # 创建砍单
        Weeebates().weeebates_create(headers=ec_login_header, orderId=orderId, orderIds=orderIds, productIds=productIds)
        Weeebates().weeebates_order_edit(headers=ec_login_header, orderId=orderId)


