# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/4/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_growth_php.weeebates import Weeebates


class TestRtgWeeeebates(weeeTest.TestCase):
    x_id = ''

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', "invalidate")
    def test_rtg_weeebate_create(self, *args, ec_zhuli_header):
        """创建RTG砍单场景验证"""
        # 获取砍单list并提取出第一个未砍单的RTG订单
        weeelist = Weeebates().weeebates_list(headers=ec_zhuli_header)
        # print(weeelist)
        data1 = weeelist["object"]["shareOrdersInfo"]
        orderId1 = []
        for share_order in data1:
            if share_order["status"] == "C" and share_order["order_type"] == "restaurant":
                orderId1.append(share_order["order_id"])
                break
        orderId = str(orderId1[0])
        TestRtgWeeeebates.x_id = orderId
        print(orderId)
        orderIds = []
        productIds = []
        for item in data1:
            if str(item["order_id"]) == orderId:
                for order in item["orders"]:
                    orderIds.append(order["order_ids"])
                    productIds.append(order["products"][0]["id"])
        # print("===>", orderIds)
        # print(productIds)
        order = []
        products = []
        data2 = {
            "orderId": orderId,
            "order": {"id": orderId, "review": "这家店真的不错，推荐给大家"},
            "orderIds": orderIds,
            "productIds": productIds,
            "products": [{"id": item, "review": "色香味俱全，回购好多次了！"} for item in productIds]
        }
        print(data2)
        # 创建砍单
        Weeebates().weeebates_rtg_create(headers=ec_zhuli_header, data=data2)
        Weeebates().weeebates_order_edit(headers=ec_zhuli_header, orderId=orderId)

