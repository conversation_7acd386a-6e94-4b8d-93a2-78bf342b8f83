# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/12/4
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_growth_php.weeebates import Weeebates


class TestWeeeebates(weeeTest.TestCase):
    x_id = ''
    x_header = ''
    @weeeTest.mark.list('Transaction','tb1')
    def test_normal_weeebate_create(self, ec_login_header):
        """创建生鲜砍单场景验证"""

        #print(headers)
        # 获取砍单list并提取出第一个未砍单的normal订单
        weeelist = Weeebates().weeebates_list(headers=ec_login_header)
        # print(weeelist)
        data1 = weeelist["object"]["shareOrdersInfo"]
        orderId1 = []
        for share_order in data1:
            if share_order["status"] == "C" and share_order["order_type"] == "normal":
                orderId1.append(share_order["order_id"])
                break
        assert orderId1, "you haven't had normal order to share"
        orderId = str(orderId1[0])
        TestWeeeebates.x_id = orderId
        print(orderId)
        orderIds = []
        productIds = []
        for item in data1:
            if str(item["order_id"]) == orderId:
                for order in item["orders"]:
                    orderIds.append(order["order_ids"])
                    productIds.append(order["products"][0]["id"])
        print("===>", orderIds)
        print(productIds)
        #创建砍单
        Weeebates().weeebates_create(headers=ec_login_header,orderId=orderId,orderIds=orderIds,productIds=productIds)
        Weeebates().weeebates_order_edit(headers=ec_login_header,orderId=orderId)

    @weeeTest.mark.list('Transaction')
    def test_share_weeebates(self):
        """砍单的分享场景验证"""

        share_data = Weeebates().weeebates_share_progress(headers=TestWeeeebates.x_header,orderId=TestWeeeebates.x_id)
        share_content = share_data["object"]["share_infos"]["share_content"]
        print(share_content)
        assert share_content, "share_content is empty"
        share_channels = share_data["object"]["share_infos"]["share_channels"]
        assert share_channels, "share_channels is empty"
        fb_image = share_data["object"]["orderShare"]["share_products_img_url"]
        assert fb_image,"facebook封面图为空"
        print(fb_image)

    @weeeTest.mark.list('Transaction')
    def test_weeebates(self):
        """未登录用户领取砍单优惠券并使用场景验证"""
        #登录好友账户
        headers = Header.login_header(email="<EMAIL>",
                                        password="a")
        #好友砍单为分享人获得砍单积分
        weeebates_rec = Weeebates().weeebates_receiver(headers=headers,orderId=TestWeeeebates.x_id)
        weeebates_pts = weeebates_rec["object"]["points"]
        print("====好友为分享人获得积分====",weeebates_pts)
