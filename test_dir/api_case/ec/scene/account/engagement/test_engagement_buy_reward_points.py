# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Zhongyuan.Xu
@Version        :  V1.0.0
------------------------------------
@File           :  test_engagement.py
@Description    :
@CreateTime     :  2023/12/6 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/12/6 15:23
"""

import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_social.engagement.engagement import Engagement


class TestEngagement(weeeTest.TestCase):
    @classmethod
    def setup_class(cls):
        cls.eg = Engagement()

    @weeeTest.mark.list( 'Transaction')
    def test_buy_reward_points(self, ec_login_header):
        """ L1或L2购买积分，不可分享 """
        # 接口已废弃
        # 1.获取登录header
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)
        # 2. L1用户购买积分
        self.eg.buy_reward_points(headers=ec_login_header, **{
            "userId": porder['object']['user_id'],
            "points": 3,
            "typeId": 28
        })
        assert self.response["object"]["pointsIssueResponseItemList"][0]["uniqBizId"] is not None
        assert self.response["object"]["pointsIssueResponseItemList"][0]["result"] is True
        assert self.response["object"]["pointsIssueResponseItemList"][0]["message"] == "success"
        # 3. 查找此用户的points, 要大于或等于上面传的参数：points
        AccountRest().me_page_pc_account_portal(headers=ec_login_header)
        sections = self.response["object"]["sections"]
        customer_points = list(filter(lambda f: f["section_name"] == "my_points", sections))[0]["data"]["item_value"]
        # “3”为每次给L1用户新加的Points
        assert customer_points >= 3, f"customer_points小于3，值为{customer_points}"
        # 4. get_me_page_url是一个非api的请求，需要增加headers["Cookie"],且结果为html文档

    # @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    # @weeeTest.mark.list( 'Transaction')
    # def test_user_buy_reward_points_and_active(self, *args):
    #     """ 用户购买未激活积分且可激活 """
    #     # l3用户专用token
    #     # 2. L3用户购买未激活积分 type=34,积分需要激活
    #     self.eg.buy_reward_points(headers=L3_user_header, **{
    #         "userId": args[0]["user"]["L1_user_id"],
    #         "points": 3,
    #         "typeId": 34
    #     })
    #     # 3. 激活积分
    #     self.eg.active_reward_points(headers=L3_user_header)
    #
    #     assert self.response["object"]["userId"] is not None
    #     assert self.response["message_id"] == "10000"

    @weeeTest.mark.list('test_check_rewards_point_upgrade', 'Regression', 'Smoke',  'Transaction')
    def test_check_rewards_point_upgrade(self, ec_login_header):
        """ # 积分升级pop 页面内容验证 """
        reward_points = AccountRest().rewards_point_upgrade(headers=ec_login_header)
        assert reward_points["result"] is True
        self.check_rewards_point_upgrade(reward_points["object"])

    def check_rewards_point_upgrade(self, reward_points):
        if reward_points["level"] < 3:
            assert reward_points["entrance_desc"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points["gold_price_str"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points["level"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points[
                       "original_gold_price_str"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points["points_product_list"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert len(reward_points["points_product_list"]) > 0, f'积分升级pop页面数据异常，请确认{reward_points}'
            if reward_points["level"] == 1:
                # 铜用户
                assert len(reward_points["points_product_list"]) == 2, f'积分升级pop页面数据异常，请确认{reward_points}'
                # 1就是升级到白银
                # 2就是升级到黄金
                assert reward_points["points_product_list"][0]["level"] == 2, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert reward_points["points_product_list"][1]["level"] == 1, f'积分升级pop页面数据异常，请确认{reward_points}'
            elif reward_points["level"] == 2:
                # 银用户
                assert len(reward_points["points_product_list"]) == 1, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert reward_points["points_product_list"][0]["level"] == 2, f'积分升级pop页面数据异常，请确认{reward_points}'
            # 积分升级选项pop 内容
            for item in reward_points["points_product_list"]:
                # Gold 的购买选项 排在第一个， Silver 排在第二个
                assert item["level"]
                assert item["buy_link_url"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert item["content"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert item["desc"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert item["level"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert item["price"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert item["product_id"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
                assert len(item["sub_contents"]) > 3, f'积分升级pop页面数据异常，请确认{reward_points}'
                # 积分升级pop 福利内容解说
                for item2 in item["sub_contents"]:
                    assert item2["icon"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
                    assert item2["title"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'

            assert reward_points["upgrade_desc"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points["upgrade_desc"][
                       "button_text"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points["upgrade_desc"]["icon"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points["upgrade_desc"][
                       "sub_title"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
            assert reward_points["upgrade_desc"]["title"] is not None, f'积分升级pop页面数据异常，请确认{reward_points}'
        else:
            pass
            # 当用户level为黄金用户时，以下断言与页面返回结果不符，先去掉
            # 金用户不返回pop
            # assert reward_points["entrance_desc"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["gold_price_str"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["original_gold_price_str"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["points_product_list"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["upgrade_desc"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # 已与xiaochong讨论，此断言可能没啥用，去掉
            # assert reward_points["entrance_desc"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["gold_price_str"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["original_gold_price_str"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["points_product_list"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'
            # assert reward_points["upgrade_desc"] is None, f'积分升级pop页面数据异常，请确认{reward_points}'

