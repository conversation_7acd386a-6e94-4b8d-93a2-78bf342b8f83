# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest


class TestRewardsLevel(weeeTest.TestCase):

    @weeeTest.mark.list('test_check_user_rewards_level', 'Regression', 'Smoke',  'Transaction')
    def test_check_user_rewards_level(self, ec_login_header):
        """ Rewards 页面验证流程 """
        rewards_level = AccountRest().get_account_rewards_level(headers=ec_login_header)
        assert rewards_level["object"] is not None, f'account_h5["object"]为{rewards_level["object"]}'
        self.rewards_level_assert(rewards_level["object"])

    def rewards_level_assert(self, rewards_level):
        account_progress_items = rewards_level["account_progress_items"]

        assert rewards_level[
                   "account_progress_items"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["background_color"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level[
                   "background_front_color"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["background_img_url"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert len(rewards_level["benefits"]) >0, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        # 页面-您的 Rewards 会员福利
        for item1 in rewards_level["benefits"]:
            assert len(item1["benefit"]) > 0, f'用户rewards_level信息返回异常，请确认{rewards_level} '
            for item2 in item1["benefit"]:
                assert item2["desc"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
                assert item2["icon_url"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
            assert item1["card_level_bg_color"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
            assert item1["card_level_text_color"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
            assert item1["level"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
            assert item1["sub_title"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
            assert item1["title"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        # assert rewards_level["buy_now"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["current_level"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["current_level_label"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["end_time"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["end_time_day"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["learn_more_url"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert "/account/rewards/landing" in rewards_level[
            "learn_more_url"], f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level[
                   "loyalty_reward_items"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert len(rewards_level["loyalty_reward_items"]) == 3, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        for item3 in rewards_level["loyalty_reward_items"]:
            if item3["level"] == 1:
                assert item3["level_title"] == "Bronze", f'用户rewards_level信息返回异常，请确认{rewards_level} '
            elif item3["level"] == 2:
                assert item3["level_title"] == "Silver", f'用户rewards_level信息返回异常，请确认{rewards_level} '
            elif item3["level"] == 3:
                assert item3["level_title"] == "Gold", f'用户rewards_level信息返回异常，请确认{rewards_level} '

        assert rewards_level["max_level_amount"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["next_end_time_day"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["next_end_time_month"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level[
                   "original_gold_price_str"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level[
                   "point_upgrade_progress_desc"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level[
                   "pre_current_amount_bg"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["progress_bg_color"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["progress_color"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["progress_desc"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["progress_num_color"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["progress_tip"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["question_button_url"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        # Rewards 省钱明细
        assert rewards_level["savings_details"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["savings_details"]["content"][
                   "desc"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["savings_details"]["content"][
                   "icon"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["savings_details"][
                   "sub_contents"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        for item4 in rewards_level["savings_details"]["sub_contents"]:
            assert item4["desc"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
            assert item4["title"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '

        assert rewards_level["share_info"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["share_info"]["meta_properties"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["share_info"]["meta_properties"]["fb_image"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["share_info"]["meta_properties"][
                   "fb_title"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["share_info"]["meta_properties"][
                   "fb_url"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert "/account/rewards/landing" in rewards_level["share_info"]["meta_properties"][
                   "fb_url"], f'用户rewards_level信息返回异常，请确认{rewards_level} '

        assert len(rewards_level["share_info"]["share_channels"]) > 0, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert "copyLink" in rewards_level["share_info"]["share_channels"], f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert len(rewards_level["share_info"]["share_content"]) > 0 is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["share_info"]["view_link"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert "/account/rewards/landing" in rewards_level["share_info"]["view_link"], f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["start_time"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["threshold"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
        assert rewards_level["tip_content"] is not None, f'用户rewards_level信息返回异常，请确认{rewards_level} '
