# -*- coding: utf-8 -*-
"""
<AUTHOR>  Zhongyuan.Xu
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/7
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.order_query.get_order_detail import GetOrderDetail


class TestUserLevel(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1', 'no_regression')
    def test_to_review_data_within_one_month(self, ec_login_header):
        """用户level升降级
        【111583】 order detail- V-point-0（购买积分）
        """
        # 因为要购买积分，所以只支持tb1
        current_level = AccountRest().get_account_rewards_level(headers=ec_login_header)
        assert current_level['object']['current_level_label'] == 'Bronze', \
            f"当前用户的等级为 {current_level['object']['current_level_label']}"

        points_upgrade = AccountRest().get_account_rewards_upgrade(headers=ec_login_header, params={"type": "my_rewards"})
        assert points_upgrade['object']['points_product_list'], f"points_upgrade={points_upgrade}"
        payment_catgory = ""
        # 109993是充值150$
        pre = PrepareCheckout().checkout_pre_no_cart(
            headers=ec_login_header,
            data={
                "product_type": "point",
                "deal_id": 494102,
                "items": [{"product_id": 109993, "quantity": 1}],
                "member_share_id": None,
                "group_invite_id": None
            }
        )
        assert pre['object']['final_amount'] == 150 and pre['object']['quantity'] == 1, f"pre={pre}"

        checkout = Checkout().no_cart_checkout(
            headers=ec_login_header,
            data={
                "product_type": "point",
                "deal_id": 494102,
                "page_time": *************,
                "points": 0,
                "items": [{"product_id": 109993, "quantity": 1}],
                "member_share_id": None,
                "group_invite_id": None,
                "payment_category": "P",
                "phone": "414***7728",
                "email": "<EMAIL>",
                "gl_tag": 314483,
                "referral_id": 0,
                "braintree_device_data": ""
            }
        )


        order_id = checkout.get('object').get('order_id')
        assert order_id > 0 and checkout.get('object').get('need_pay'), f"checkout={checkout}"
        order_detail = GetOrderDetail().get_order_detail(headers=ec_login_header,
                                                         order_id=str(order_id))
        assert order_detail.get('object').get('order_status_info').get('status_class') == 'order-status-created'
        assert order_detail.get('object').get('order_status_info').get('title') == 'Pending: action required'
        assert order_detail.get('object').get('order_status_info').get('is_not_delivered') is False

        assert order_detail.get('object').get('btnList') == ['pay', 'cancel']


