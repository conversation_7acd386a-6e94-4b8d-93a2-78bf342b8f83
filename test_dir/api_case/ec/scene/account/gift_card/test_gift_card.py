# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2024/10/13 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
from typing import Any

import weeeTest

from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder



class TestGiftCard(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction','test_pdp_gift_card')
    def test_pdp_gift_card(self, ec_login_header):
        """ Product-pdp礼品卡验证流程 """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        product_id = 2189607
        # 返回冷链商品pdp的所有信息，包括product group
        pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header,
                                            product_id=product_id, zipcode=porder["zipcode"],
                                            sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"][
                   "gift_card_info"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'
        self.pdp_gift_card_info_assert(pdp_detail["object"]["gift_card_info"])

    def pdp_gift_card_info_assert(self, gift_card_info):
        assert gift_card_info["default_sender_name"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
        assert len(gift_card_info["infos"]) > 0, f'礼品卡pdp信息异常{gift_card_info}'
        for item in gift_card_info["infos"]:
            assert item["language"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
            assert len(item["categories"]) > 0, f'礼品卡pdp信息异常{gift_card_info}'
            for item2 in item["categories"]:
                assert item2["category_id"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
                assert item2["category_name"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
                assert len(item2["themes"]) > 0, f'礼品卡pdp信息异常{gift_card_info}'
                for item3 in item2["themes"]:
                    assert item3["theme_id"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
                    assert item3["cover_img"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
                    assert item3["default_message"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
        assert len(gift_card_info["languages"]) > 0, f'礼品卡pdp信息异常{gift_card_info}'
        assert gift_card_info["quantity_limit"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
        assert gift_card_info["user_id"] is not None, f'礼品卡pdp信息异常{gift_card_info}'
        assert gift_card_info["default_sender_name"] is not None, f'礼品卡pdp信息异常{gift_card_info}'