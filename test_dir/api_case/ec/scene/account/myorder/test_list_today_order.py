# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_so.order_query.list_today_order import ListTodayOrder
from test_dir.api.ec.ec_so.order_query.track_order import TrackOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestListTodayOrder(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Transaction')
    def test_list_today_order(self, ec_login_header):
        """ 我的订单-今日订单详情验证流程 """
        # 线上无法创建已发货的订单，此用例无法验证
        ListTodayOrder().list_today_order(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True, f"self.response是：{self.response}"
        assert self.response['object']['user']['global_user_id'] == 12491964, f"self.response['object']['user']['global_user_id']是{self.response['object']['user']['global_user_id']}"
        assert CommonCheck.list_check(['today_orders_num', 'show_have_today', 'have_today_delivery'], self.response["object"].keys()), f"self.response是：{self.response}"
        today_order = self.response["object"]
        # todo @suqin today_order["today_orders_num"] > 0 此条件未满足，分支未到达
        if today_order["today_orders_num"] > 0:
            delivery_data = today_order["delivery_data"]
            for delivery in delivery_data:
                delivery_order = delivery["delivery_order"]
                for order in delivery_order:
                    order_id = order["id"]
                    # 点击查看物流
                    TrackOrder().track_order(headers=ec_login_header, order_id=order_id)
                    # 断言
                    assert self.response["result"] is True
                    break

    @weeeTest.mark.list('Regression', 'Transaction')
    def test_list_today_order_me_page(self, ec_login_header):
        """ # Me页面-今日订单验证流程 """

        ListTodayOrder().list_today_order_me_page(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
        assert self.response['object']['section_type'] == 'today_orders' and self.response['object']['section_name'] == 'my_today_orders' and self.response['object']['section_title'] == "Today's Orders", f"self.response['object']是：{self.response['object']}"
        assert 'today-order' in self.response['object']['link_url']
        assert CommonCheck.list_check(['today_orders_num', 'show_have_today', 'have_today_ondemand'],
                                      self.response["object"]["data"].keys()), f"self.response是：{self.response}"

