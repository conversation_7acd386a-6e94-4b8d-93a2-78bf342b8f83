# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import time

import weeeTest

from test_dir.api.ec.ec_so.order_query.buy_again_info import BuyAgainInfo
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestListMyOrder(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Transaction','product','test_buy_again_order_v1')
    @weeeTest.mark.timeout(10000)
    def test_buy_again_order_v1(self, ec_login_header):
        """我的订单-再来一单验证流程 """
        # 可能会在线上下单，去掉Smoke, Regression标签
        # ["all","1","2","3","6","4"]
        filter_status = ["all", "1", "2", "3", "6", "4"]
        for item in filter_status:
            listall = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status=item)
            # 断言 2024年有2个真实订单数据
            assert listall["result"] is True
            assert CommonCheck.list_check(["total", "user", "myOrders"], listall["object"].keys())
            # assert listall["object"]["total"] > 0
            my_order = listall["object"]
            if my_order["total"] > 0:
                myOrders = my_order["myOrders"]
                for index, order in enumerate(myOrders):
                    # 再来一单
                    buy_again = BuyAgainInfo().buy_again_info_v2(headers=ec_login_header, order_id=order["id"])
                    assert buy_again["result"] is True
                    # 有些订单里面商品不可购买，断言会失败，所以去掉此断言
                    # assert len(self.response['object'][
                    #                'items']) > 0, f"self.response['object']['items'] is {self.response['object']['items']}"
                    assert CommonCheck.list_check(["deliveryDate", "delivery_mode", "items"],
                                                  buy_again["object"].keys()), f"self.response is {buy_again}"

                    if index == 3:
                        break
