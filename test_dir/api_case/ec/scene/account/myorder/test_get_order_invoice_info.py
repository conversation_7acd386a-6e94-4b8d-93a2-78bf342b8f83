# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from test_dir.api.ec.ec_so.order_query.get_order_invoice_info import GetOrderInvoiceInfo


class TestGetOrderInvoiceInfo(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'test_get_order_invoice_info')
    def test_get_order_invoice_info(self, *args, ec_login_header):
        """ 我的订单-查看invoice页面验证 """
        # 线上订单数据不满足测试场景
        # 获取已发货订单，拿到invoice_id;H5要用list_my_order_v2
        invoice_id = args[0]["listmyorder"]["invoice_id"]
        invoice_info = GetOrderInvoiceInfo().get_order_invoice_info(ec_login_header,
                                                                    str(invoice_id))
        assert invoice_info["result"] is True, f'invoice 信息异常{invoice_info}'
        # assert invoice_info['object']["total"] > 0, f'invoice 信息异常{invoice_info}'
        self.invoice_info_assert(invoice_info["object"])

    def invoice_info_assert(self, invoice_info):
        assert invoice_info["title"] is not None, f'invoice 信息异常{invoice_info}'
        assert invoice_info["invoice"]["id"] is not None, f'invoice 信息异常{invoice_info}'
        assert invoice_info["invoice"]["address"] is not None, f'invoice 信息异常{invoice_info}'
        assert invoice_info["invoice"]["order_user_name"] is not None, f'invoice 信息异常{invoice_info}'
        assert len(invoice_info["invoice"]["delivery_products"]) > 0, f'invoice 信息异常{invoice_info}'
        assert invoice_info["invoice"]["phone"] is not None, f'invoice 信息异常{invoice_info}'
        assert invoice_info["invoice"]["delivery_date"] is not None, f'invoice 信息异常{invoice_info}'
        assert invoice_info["invoice"]["sub_total"] >= 0, f'invoice 信息异常{invoice_info}'
        assert invoice_info["invoice"]["total"] >= 0, f'invoice 信息异常{invoice_info}'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
