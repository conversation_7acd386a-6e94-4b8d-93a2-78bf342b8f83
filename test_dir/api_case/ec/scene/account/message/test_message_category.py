# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> she
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2024/10/10
@Software       :  PyCharm
------------------------------------
"""
import time

import pytest
import weeeTest
from test_dir.api.ec.ec_customer.notification.central_notification import CentralNotification


class TestMessageCategory(weeeTest.TestCase):
    @pytest.fixture(scope='class')
    def setup(self, ec_login_header):
        """
        前置条件 消息中心

        """
        # 参数对象全局，其他case中直接使用
        messages = CentralNotification().message_category(headers=ec_login_header)
        if not isinstance(messages, dict):
            # 这个接口容易超时，所以加上重试
            time.sleep(10)
            messages = CentralNotification().message_category(headers=ec_login_header)
        sub_category = CentralNotification().sub_category(headers=ec_login_header, category="community")
        return messages, sub_category

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'test_message')
    def test_message_category(self, setup):
        """# 消息中心-消息列表验证流程"""
        assert setup[0].get("result") is True, f'消息中心数据返回异常{setup[0]}'
        assert len(setup[0].get("object")) > 0, f'消息中心数据返回异常{setup[0]}'
        self.message_center_assert(setup[0]["object"])

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'test_message')
    def test_message_sub_category_list(self, setup, ec_login_header):
        """消息中心-消息子分类列表验证流程"""
        for item in setup[0]["object"]:
            sub_list = CentralNotification().message_sub_category_list(headers=ec_login_header,
                                                                       category_id=item["category_id"]
                                                                       )
            assert sub_list["result"] is True, f'消息中心数据返回异常{sub_list}'
            assert sub_list["object"]["title"] is not None, f'消息中心数据返回异常{sub_list}'
            if len(sub_list["object"]["list"]) > 0:
                pass

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'test_message')
    def test_sub_category(self, setup, ec_login_header):
        """消息中心-社区活动子分类列表验证流程"""
        assert setup[1]["result"] is True, f'消息中心数据返回异常{setup[1]}'
        assert len(setup[1]["object"]) > 0, f'消息中心数据返回异常{setup[1]}'
        for item in setup[1]["object"]:
            # 子分类断言
            self.message_sub_category_assert(item)
            # 切换子分类
            sub_list = CentralNotification().message_sub_category_list(headers=ec_login_header,
                                                                       category_id=item["category_id"]
                                                                       )
            assert sub_list["result"] is True, f'消息中心数据返回异常{sub_list}'
            assert sub_list["object"]["title"] is not None, f'消息中心数据返回异常{sub_list}'

    def message_center_assert(self, messages):
        required_messages = ["order", "rewards", "community", "ProductRecommendations",
                             "system", "cs", "SellerMessages"]
        # 获取messages中所有的category
        categorys = [item["category"] for item in messages]

        # 断言匿名用户返回
        assert all(required_name in categorys for required_name in
                   required_messages), f"Not all required section_names are present"

        for item in messages:
            assert item["content"] is not None, f'消息中心数据返回异常{messages}'
            assert item["icon_url"] is not None, f'消息中心数据返回异常{messages}'
            assert item["title"] is not None, f'消息中心数据返回异常{messages}'
            assert item["view_url"] is not None, f'消息中心数据返回异常{messages}'
            # "我的订单"
            if item["category"] == "order":
                assert item["category_id"] == 1, f'消息中心数据返回异常{messages}'
                assert "account/notification/order" in item["view_url"], f'消息中心数据返回异常{messages}'
            # "奖励"
            elif item["category"] == "rewards":
                assert item["category_id"] == 2, f'消息中心数据返回异常{messages}'
                assert "account/notification/rewards" in item["view_url"], f'消息中心数据返回异常{messages}'
            # "社区活动"
            elif item["category"] == "community":
                assert item["category_id"] == 3, f'消息中心数据返回异常{messages}'
                assert "account/notification/community" in item["view_url"], f'消息中心数据返回异常{messages}'
            # "推荐商品"
            elif item["category"] == "ProductRecommendations":
                assert item["category_id"] == 28, f'消息中心数据返回异常{messages}'
                assert "account/notification/ProductRecommendations" in item[
                    "view_url"], f'消息中心数据返回异常{messages}'
            # "系统消息"
            elif item["category"] == "system":
                assert item["category_id"] == 4, f'消息中心数据返回异常{messages}'
                assert "account/notification/system" in item["view_url"], f'消息中心数据返回异常{messages}'
            # "Weee! 客服"
            elif item["category"] == "cs":
                assert item["category_id"] == 5, f'消息中心数据返回异常{messages}'
                assert "account/notification/cs" in item["view_url"], f'消息中心数据返回异常{messages}'
            # 商家消息
            elif item["category"] == "SellerMessages":
                assert item["category_id"] == 29, f'消息中心数据返回异常{messages}'
                assert "account/notification/SellerMessages" in item["view_url"], f'消息中心数据返回异常{messages}'

    def message_sub_category_assert(self, item):
        assert item["category"] is not None, f'消息中心数据返回异常{item}'
        assert item["parent_title"] is not None, f'消息中心数据返回异常{item}'
        assert item["title"] is not None, f'消息中心数据返回异常{item}'
        assert item["view_url"] is not None, f'消息中心数据返回异常{item}'
        assert item["category"] is not None, f'消息中心数据返回异常{item}'
        # "全部"
        if item["category"] == "CommunityAll":
            assert item["category_id"] == 9, f'消息中心数据返回异常{item}'
            assert "account/notification/CommunityAll" in item["view_url"], f'消息中心数据返回异常{item}'

        # "粉丝"
        elif item["category"] == "CommunityFollow":
            assert item["category_id"] == 10, f'消息中心数据返回异常{item}'
            assert "account/notification/CommunityFollow" in item["view_url"], f'消息中心数据返回异常{item}'

        # "评论"
        elif item["category"] == "CommunityComment":
            assert item["category_id"] == 11, f'消息中心数据返回异常{item}'
            assert "account/notification/CommunityComment" in item["view_url"], f'消息中心数据返回异常{item}'
        # "点赞"
        elif item["category"] == "CommunityLike":
            assert item["category_id"] == 12, f'消息中心数据返回异常{item}'
            assert "account/notification/CommunityLike" in item["view_url"], f'消息中心数据返回异常{item}'
        # "最新动态"
        elif item["category"] == "CommunityUpdates":
            assert item["category_id"] == 13, f'消息中心数据返回异常{item}'
            assert "account/notification/CommunityUpdates" in item["view_url"], f'消息中心数据返回异常{item}'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
