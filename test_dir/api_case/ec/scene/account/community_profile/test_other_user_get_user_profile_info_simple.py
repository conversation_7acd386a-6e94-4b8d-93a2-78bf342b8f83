# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile


class TestOtherUserGetUserProfileInfo(weeeTest.TestCase):
    @weeeTest.mark.list('test_get_user_coupon_info', 'Regression-skip', 'Social', 'Smoke-restore',  'Transaction')
    def test_other_user_get_user_profile_info(self, ec_login_header):
        """ Me-Community Profile 页面信息验证"""
        # 参数不对，需要修复
        profile_info = UserProfile().other_user_get_user_profile_info(headers=ec_login_header, uid='7790952')
        assert profile_info["result"] is True
        self.check_user_profile_info(profile_info)

    def check_user_profile_info(self, profile_info):
        assert profile_info["bio_url"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert "/account/bio" in profile_info["bio_url"], f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["profile_status"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["profile_url"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert "/account/settings" in profile_info["profile_url"], f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["seller_link_text"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["seller_url"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["social_info"] is not None, f'Community Profile 页面信息异常{profile_info}'
        for item in profile_info["social_info"]:
            assert item["label"] is not None, f'Community Profile 页面信息异常{profile_info}'
            assert item["value"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["upgrade_popup"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["user_info"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["user_info"]["head_img_url"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["user_info"]["username"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["user_info"]["uid"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["user_info"]["user_id"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["user_star_info"] is not None, f'Community Profile 页面信息异常{profile_info}'
        assert profile_info["user_tier_info"] is not None, f'Community Profile 页面信息异常{profile_info}'

