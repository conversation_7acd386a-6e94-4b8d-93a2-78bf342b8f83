# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile


class TestUserSelfGetUserProfileInfo(weeeTest.TestCase):

    @weeeTest.mark.list('test_get_user_coupon_info', 'Regression', 'Social', 'Smoke',  'Transaction')
    def test_user_self_get_user_profile_info(self, ec_login_header):
        """获取用户对应的profile信息"""
        profile_info = UserProfile().user_self_get_user_profile_info(headers=ec_login_header)
        assert profile_info["result"], f"resp={profile_info}"
        assert profile_info.get('object').get('user_info').get('user_id') == 12491964, f"resp={profile_info}"
        assert profile_info.get('object').get('user_info').get('alias') == 'autotest', f"resp={profile_info}"
        assert profile_info.get('object').get('user_tier_info'), f"resp={profile_info}"
        assert profile_info.get('object').get('user_star_info'), f"resp={profile_info}"


