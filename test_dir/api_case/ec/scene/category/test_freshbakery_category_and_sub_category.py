# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  zhongyuan.xu
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2024/10/8 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/10/8 15:23
"""
import json

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestFreshBakeryCategoryAndL2Sub(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'product')
    def test_fresh_bakery_and_sub_category(self, ec_login_header):
        """
        【100207】 验证原Bakery L1&L2 分类商品列表
        """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        fresh_bakery = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="freshbakery"
        )

        sub_categories = [item.get('catalogue_num') for item in fresh_bakery.get('object').get('categories')]
        assert sub_categories, f"zipcode={zipcode}下没有fresh bakery的子category, sub_categories={sub_categories}"
        for sub_category in sub_categories:
            sub_fresh_bakery = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                zipcode=zipcode,
                date=deal_date,
                filter_sub_category="freshbakery",
                filters=json.dumps({"catalogue_num": sub_category}),
                out_filters=json.dumps({})
            )
            assert sub_fresh_bakery['object']['contents'], f"fresh bakery没有数据，sub category={sub_category}, sub_fresh_bakery={sub_fresh_bakery}"
            for item in sub_fresh_bakery['object']['contents']:
                assert "freshbakery" in item.get('data').get('default_parent_category'), f"item={item}, sub={sub_category}"
                assert "freshbakery" in item.get('data').get('parent_category'), f"item={item}, sub={sub_category}"
                assert sub_category in item.get('data').get('tag_list'), f"item={item}, sub={sub_category}"
                # assert "Fresh daily" in item.get('data').get('activity_tag_list'), f"item={item}, sub={sub_category}"
