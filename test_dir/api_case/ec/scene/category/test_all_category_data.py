# # !/usr/bin/python3
# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @File           :  test_user.py
# @Description    :
# @CreateTime     :  2023/5/16 15:23
# @Software       :  PyCharm
# ------------------------------------
# @ModifyTime     :  2023/5/16 15:23
# """
#
# import requests
# import weeeTest
# from weeeTest import log,  RequestHeader
# import json
# from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest
# from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
# from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
# from test_dir.api.ec.ec_item.store.api_store import ApiStore
# from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
# from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
#
#
#
# class TestAllGroceryData(weeeTest.TestCase):
#
#     @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
#     @weeeTest.mark.list('ACAD', 'Transaction','tb1')
#     # @weeeTest.mark.xfailif(condetion=None, reasion="预期失败的case")
#     def test_all_category_data(self, *args):
#         """ 分类-验证所有销售组织/语言/store下有分类数据返回 """
#         # 获取不同销售组织下不同zipcode
#         sales_data = args[0]["sales_data"]
#         for sale_data in sales_data:
#             zipcode = sale_data["zipcode"]
#
#             # 切换不同销售组织的zipcode
#             UpdateZipcode().update_zipcode_v1(headers=RequestHeader.ec_login_header, zipcode=zipcode)
#             if self.response["message_id"] == "SO90029":
#                 print("这个地区没有帖子")
#             else:
#                 # 断言
#                 assert self.response["result"] is True
#                 # 切换语言
#                 lang = ["en", "zh", "zh-Hant", "ja", "ko", "vi"]
#                 for lang in lang:
#                     # 切换用户语言
#                     LanguageRest().update_account_language(headers=RequestHeader.ec_login_header, lang=lang)
#                     if lang == "en":
#                         # 获取store
#                         ApiStore().store_list(headers=RequestHeader.ec_login_header, lang=lang, zipcode=int(zipcode))
#                         if self.response["object"] is not None:
#                             for store in self.response["object"]:
#                                 store_id = store["store_id"]
#                                 # 切换store
#                                 ApiStore().store_select(headers=RequestHeader.ec_login_header, store_id=store_id,
#                                                         zipcode=int(zipcode))
#                                 # 断言
#                                 assert self.response["result"] is True
#
#                     # 获取用户的preorder
#                     porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=RequestHeader.ec_login_header)["object"]
#                     # 获取所有一级普通分类,不包括特殊分类
#                     ApiCatalogues().catalogue_home(headers=RequestHeader.ec_login_header)
#                     assert self.response["result"] is True
#                     category_list = self.response["object"]["category_list"]
#                     # 根据获取到的一级分类，在访问对应的普通分类
#                     for category in category_list:
#                         if category["num"] == "global" or category["num"] == "mooncake" or category["num"] == "lny":
#                             continue
#                         try:
#                             # 访问普通分类
#                             SearchByCatalogueContent().search_by_catalogue_content(headers=RequestHeader.ec_login_header,
#                                                                                    filter_sub_category=category["num"])
#                             assert self.response["result"] is True
#
#                             # 断言每个分类下必须要有数据
#                             assert self.response["object"]["total_count"] > 0
#                         except Exception as e:
#                             log.error("普通分类数据异常" + str(e))
#
#                         # write_debug_log_on_jenkins("./logs/porder.txt",
#                         #                            json.dumps(self.response) + "headers:" + json.dumps(headers) + "\n")
#
#                     # 获取特殊分类
#                     category_list = args[0]["category"]["search_by_catalogue"]
#                     # 获取特殊分類
#                     for category in category_list:
#                         try:
#                             # 访问特殊分类
#                             SearchByCatalogueContent().search_by_catalogue_content(headers=RequestHeader.ec_login_header,
#                                                                                    filter_sub_category=category[
#                                                                                        "filter_sub_category"],
#                                                                                    date=porder["delivery_pickup_date"],
#                                                                                    zipcode=porder["zipcode"]
#                                                                                 )
#                             # 断言每个分类下必须要有数据
#                             assert self.response["object"]["total_count"] > 0
#                         except Exception as e:
#                             log.error("特殊分类数据异常" + str(e))
#             break
#
#
# if __name__ == '__main__':
#     weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
