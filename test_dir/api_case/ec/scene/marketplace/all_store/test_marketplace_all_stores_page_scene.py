# !/usr/bin/python3
# -*- coding: utf-8 -*-

import re

import weeeTest
from weeeTest import jmespath
from weeeTest import weeeConfig
from test_dir.api_case.ec.common.common_check_mkpl import AllStoreCheck
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestMkplAllStoresPageScene(weeeTest.TestCase):
    """
    Global+ waterfall页面
    """

    @weeeTest.mark.list('Regression', 'Transaction')
    def test_stores_sort(self, ec_mkpl_header):
        """
        store排序逻辑:大数据返回顺序：总销量倒序
        """
        # fixture取值
        header, zipcode, sales_org_id = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id']
        date = ec_mkpl_header['date']
        # AllStorePage类实例
        all_store_page = mkpl.AllStorePage()
        # 产地
        origin_list = all_store_page.store_tabs(header)
        # allstore 页面cms>最后一个组件 store list >组件详情
        tab_cms_config = all_store_page.tab_cms_config(headers=header, tab=origin_list[0]['page_key'],
                                                       sales_org_id=sales_org_id,
                                                       zipcode=zipcode)
        page_component_keys = all_store_page.page_component_keys(tab_cms_config)
        seller_list_component = page_component_keys[-1]
        all_store_detail = all_store_page.cms_component_detail(headers=header,
                                                               tab_key=origin_list[0]['origin_id'],
                                                               cms_component=seller_list_component)
        # seller tag最后一个为销量str
        seller_volume_tags = jmespath(all_store_detail, "object.data[*].seller_tags[-1].tag")
        volume_lst = []
        for volume in seller_volume_tags:
            # 正则取出tag中数字:str
            # 销量>1k 正则
            pattern1 = re.compile(r'^\d+k\+ sold$')
            # 销量<1k 正则
            pattern2 = re.compile(r'^\d+\+ sold$')
            if pattern1.match(volume):
                sold_str = re.findall(r'\d+', volume)[0]
                sold_str = sold_str + '000'
                volume_lst.append(int(sold_str))
            elif pattern2.match(volume):
                sold_str = re.findall(r'\d+', volume)
                volume_lst.append(int(sold_str))
        # 按照数值大小倒序
        sort_volume_lst = sorted(volume_lst, key=lambda x: int(x), reverse=True)
        # 断言排序后与排序前销量顺序一致
        assert sort_volume_lst == volume_lst

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_switch_origins(self, ec_mkpl_header):
        # fixture取值 mkpl_common_headers zipcode sales_org_id date
        header, zipcode, sales_org_id = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id']
        date = ec_mkpl_header['date']
        # AllStorePage类实例
        all_store_page = mkpl.AllStorePage()
        # 产地
        origin_list = all_store_page.store_tabs(header)
        # 推荐，JA, KO等各地区商家切换,tab切换成功，展示对应国家的商家
        for origin in origin_list:
            tab = origin['page_key']
            # tab = origin['origin_id']
            tab_cms_config = all_store_page.tab_cms_config(headers=header, tab=tab,
                                                           sales_org_id=sales_org_id,
                                                           zipcode=zipcode)
            page_component_keys = all_store_page.page_component_keys(tab_cms_config)
            seller_list_component = page_component_keys[-1]
            all_store_detail = all_store_page.cms_component_detail(headers=header,
                                                                   tab_key=origin['origin_id'],
                                                                   cms_component=seller_list_component)
            sellers = all_store_detail['object']['data']
            for seller in sellers:
                # 断言商家卡片信息
                AllStoreCheck().check_singe_seller_card(seller)
                seller_card_products = seller['products']
                for product in seller_card_products:
                    # 断言商家卡片中商品
                    AllStoreCheck().check_seller_card_product(product)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
