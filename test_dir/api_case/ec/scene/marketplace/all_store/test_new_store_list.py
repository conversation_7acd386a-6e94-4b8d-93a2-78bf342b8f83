import weeeTest

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_new_store import GlobalNewStore
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestGlobalNewStoreList(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'test_global_new_store_list')
    def test_global_new_store_list(self, ec_mkpl_header):
        """ # 获取All store页面新店上线信息"""
        global_new_store = GlobalNewStore().global_new_store(headers=ec_mkpl_header['addr_header'])
        assert global_new_store["result"] is True, f'新店上线信息数据异常{global_new_store}'
        assert len(global_new_store.get("object")) > 0, f'新店上线信息数据异常{global_new_store}'
        for item in global_new_store.get("object"):
            self.global_new_store_assert(store_list=item, headers=ec_mkpl_header['addr_header'])

    def global_new_store_assert(self, store_list, headers):
        seller_id = store_list.get("seller_id")
        assert seller_id is not None, f'新店上线信息数据异常{store_list}'
        assert store_list.get("more_link") is None, f'新店上线信息数据异常{store_list}'
        assert store_list.get("title") is not None, f'新店上线信息数据异常{store_list}'
        assert store_list.get("shipping_reminder_content") is not None, f'新店上线信息数据异常{store_list}'

        # if origin_data.get("overall_rating") > 3:
        #     assert origin_data.get("overall_rating"), f'全部商店数据异常{origin_data}'
        assert store_list.get("products") is not None, f'新店上线信息数据异常{store_list}'
        for item1 in store_list.get("products"):
            assert item1.get("biz_type") == "seller", f'新店上线信息数据异常{store_list}'

        seller_tags = store_list.get("seller_tags")
        if len(seller_tags) > 0:
            for item2 in seller_tags:
                assert item2.get("background_color") is not None, f'全部商店数据异常{store_list}'
                assert item2.get("font_color") is not None, f'全部商店数据异常{store_list}'
                assert item2.get("tag") is not None, f'全部商店数据异常{store_list}'
                assert item2.get("type") is not None, f'全部商店数据异常{store_list}'

        assert store_list.get("seller_url") is not None, f'新店上线信息数据异常{store_list}'
        assert "/mkpl/vendor/" + str(seller_id) in store_list.get("seller_url"), f'新店上线信息数据异常{store_list}'
        CommCheckFunction().comm_check_link(store_list.get("seller_url"), headers=headers)
