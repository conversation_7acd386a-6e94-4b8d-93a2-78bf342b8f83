# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import weeeConfig, log, jmespath

from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_store_all import GlobalStore
from test_dir.api.ec.ec_so.address.add_or_edit_user_address import AddOrEditUserAddress
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.seller.marketplacea_seller_cart import SellerCart

from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestMarketPlaceOrderScene(weeeTest.TestCase):
    """
    marketplace加购下单结算
    """

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'tb1', 'Transaction')
    def test_global_place_order(self, *args, ec_mkpl_header):
        """ marketplace加购下单结算 """
        # 1 登录
        addr_lst = QueryUserAddressList().address_list(ec_mkpl_header.get("addr_header"))

        # 3 判断地址簿是否为空，为空则添加地址
        if addr_lst['object'] is None:
            AddOrEditUserAddress().add_address(
                headers=ec_mkpl_header.get("addr_header"),
                addr_firstname=args[0]["scene_config"]["addr"]["addr_firstname"],
                addr_lastname=args[0]["scene_config"]["addr"]["addr_lastname"],
                phone=str(args[0]["scene_config"]["login_scene"]["phone"]),
                comment="",
                email=args[0]["scene_config"]["login_scene"]["email"],
                addr_address=args[0]["scene_config"]["addr"]["addr_address"],
                addr_zipcode=args[0]["scene_config"]["addr"]["addr_zipcode"],
                addr_apt=args[0]["scene_config"]["addr"]["addr_apt"],
                addr_city=args[0]["scene_config"]["addr"]["addr_city"],
                addr_state=args[0]["scene_config"]["addr"]["addr_state"],
                force=True
            )
            addr_lst = QueryUserAddressList().address_list(headers=ec_mkpl_header.get("addr_header"))
            log.info(">>>>>{addr_lst}")
        # 4 取地址簿第一个地址
        first_addr_id = addr_lst['object'][0]['address_id']
        first_addr_zipcode = addr_lst['object'][0]['addr_zipcode']
        # 5 更新header zipcode
        ec_mkpl_header.get("addr_header")['Zipcode'] = first_addr_zipcode
        simple_res = QuerySimplePreOrder().query_simple_preorder_v1(ec_mkpl_header.get("addr_header"))
        first_addr_sales_org_id = jmespath(simple_res, "object.sales_org_id")
        delivery_date = jmespath(simple_res, "object.delivery_date")
        # 6 清空已有购物车
        RemoveAllProductsInCart().clear_grocery_cart(headers=ec_mkpl_header.get("addr_header"))
        # 7 请求global+ all store 第一个seller 第一个product（page cms>cms object>cms detail>product seller ）
        recommend_page_cms = QueryPageData().query_page_data(
            headers=ec_mkpl_header.get("addr_header"), page_key="recommend",
            page_type=str(8),
            lang='zh',
            sales_org_id=first_addr_sales_org_id,
            zipcode=first_addr_zipcode, mode=None
        )
        # 获取对应cms组件 key
        data_obj_key = jmespath(recommend_page_cms, "object.layout.sections")[0]["components"][-1][
            "component_config_id"]
        cms_key = "mkpl_seller_list_" + str(data_obj_key)
        # 请求组件详情
        cms_detail = GlobalStore().global_store_all(
            headers=ec_mkpl_header.get("addr_header"), origin_ids=0, dataobject_key=cms_key,
            zipcode=first_addr_zipcode,
            pagesize=10, page_no=0, lang='zh', date=delivery_date
        )
        # 取详情第一个商品对应product、seller
        seller_id = jmespath(cms_detail, "object.data")[0]["seller_id"]
        print(">>>>>>>seller_id", seller_id)
        product_id = jmespath(cms_detail, "object.data")[0]["products"][0]["id"]
        print(">>>>>>>product_id", product_id)
        # mkpl 加购
        cart = SellerCart().seller_cart_action(
            headers=ec_mkpl_header.get("addr_header"), product_id=product_id, qty=1,
            seller_id=seller_id,
            delivery_date=delivery_date, refer_type="seller"
        )
        # porderV5
        QueryPreOrder().query_preorder_v5(headers=ec_mkpl_header.get("addr_header"), cart_domain="grocery")
        # apply 地址
        ApplyUserAddressInformation().address_apply(ec_mkpl_header.get("addr_header"), address_id=first_addr_id)
        ApplyUserAddressInformation().address_apply_v2(ec_mkpl_header.get("addr_header"), address_id=first_addr_id)

        pre = PrepareCheckout().prepare_checkout_v2_marketplace(headers=ec_mkpl_header.get("addr_header"), cart_domain="grocery")
        print(">>>>>>>seller_id", pre["object"])
        # checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_amount = jmespath(pre, "object.fee_info.final_amount")
        checkout_pre_id = jmespath(pre, "object.checkout_pre_id")
        # checkoutV3带cvv token token写死
        order_res = Checkout().checkout_v3_cvc(
            headers=ec_mkpl_header.get("addr_header"), cart_domain="grocery",
            checkout_pre_id=checkout_pre_id,
            checkoutAmount=checkout_amount,
            cvv_token="tokencc_bj_nqn64k_xt3x2v_g4xgpv_hfzxtx_8x3"
        )
        order_id = jmespath(order_res, "object.order_ids")[0]
        log.info(f"marketplace订单{order_id}")
        assert type(order_id) == int
        print(order_id)
        # 通过下单返回need_pay 字段判断订单pending 或success 取消对应状态订单
        if order_res['object']['need_pay']:
            print(">>>>>>>order_res['object']['need_pay']:", order_res['object']['need_pay'])
            CancelOrder().cancel_unpaid_order(headers=ec_mkpl_header.get("addr_header"), order_id=str(order_id))
        else:
            print(">>>>>>>order_res['object']['need_pay']", order_res['object']['need_pay'])
            CancelOrder().cancel_order(headers=ec_mkpl_header.get("addr_header"), order_id=order_id)
        # 取消订单列表订单
        cancelled_order = ListMyOrder().list_my_order_v2(headers=ec_mkpl_header.get("addr_header"), filter_status='4')
        cancelled_order_list = jmespath(cancelled_order, "object.myOrders[*].id")
        # 断言 创建并取消的订单号在取消订单列表
        assert order_id in cancelled_order_list, f'创建订单id：{order_id}，cancelled order list {cancelled_order_list}'


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
