# !/usr/bin/python3
# -*- coding: utf-8 -*-
import re

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_item.search_vender_rest.search_vender import SearchVender
from test_dir.api.ec.ec_so.seller.marketplacea_seller_cart import Seller<PERSON>art
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl
from test_dir.api_case.ec.scene.marketplace.mkpl_util import AllStorePage
import pytest

percent_promotion_pattern = r'Spend \$(\d+(\.\d{1,2})?)\+, get (\d{1,2})\% off'
fixed_promotion_pattern = r'Spend \$(\d+(\.\d{1,2})?)\+, get \$(\d+(\.\d{1,2})?) off'
gift_promotion_pattern = r'Spend \$(\d+(\.\d{1,2})?), get free gift'






class TestSellerPromotionScene(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        """
        前置条件 登录加载global+页面cms
        recommend_session：请求waterfall需要前端随机生成，自动化暂时使用AUTO-TEST+时间


        """
        # 参数对象全局
        global zipcode, sales_org_id, date, buy_x_get_x_off_seller, percent_promotion_seller
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))
        zipcode, sales_org_id =  common['zipcode'], common[
            'sales_org_id']
        date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        recommend_session = mkpl.Common().recommend_session
        # GlobalPlusPage类实例
        global_plus_page = mkpl.GlobalPlusPage()
        # 类方法请求页面cms
        global_plus_page_cms = global_plus_page.cms_config(ec_mkpl_header.get("addr_header"), zipcode, sales_org_id)
        # 类方法取cms中组件key组成列表返回
        component_key_lst = global_plus_page.page_component_keys(global_plus_page_cms)
        all_store = AllStorePage()
        # onsale 返回的商家id list
        onsale_cms = QueryPageData().query_page_data(headers=ec_mkpl_header.get("addr_header"), page_type='8', page_key='recommend',
                                                     lang=ec_mkpl_header.get("addr_header")['Lang'], sales_org_id=sales_org_id,
                                                     zipcode=zipcode)
        for component in jmespath(onsale_cms, 'object.layout.sections[0].components'):
            if component['component_key'] == 'cm_mkpl_seller_line':
                onsale_list = all_store.cms_component_detail(ec_mkpl_header.get("addr_header"), tab_key='recommend',
                                                             cms_component=component)
        onsale_sellers = onsale_list['object']
        promo_sellers = [seller for seller in onsale_sellers if "promo" in jmespath(seller, 'seller_tags[*].type')]
        buy_x_get_x_off_seller = [seller for seller in promo_sellers if len([tag for tag in seller['seller_tags'] if
                                                                             re.match(fixed_promotion_pattern,
                                                                                      tag['tag'])]) > 0]
        percent_promotion_seller = [seller for seller in promo_sellers if len([tag for tag in seller['seller_tags'] if
                                                                               re.match(percent_promotion_pattern,
                                                                                        tag['tag'])]) > 0]

    def test_seller_buy_x_get_x_off_label(self, ec_mkpl_header):
        """
        验证商家已设置满减门槛活动 商品tag label展示活动信息：买$xxx，立减$xx
        """
        if len(buy_x_get_x_off_seller) == 0:
            pytest.skip('No buy_x_get_x_off seller promotion ')
        else:
            promo_seller = buy_x_get_x_off_seller[0]
            seller_id = promo_seller['seller_id']

            all_products = SearchVender().vender_all_tab_products(headers=ec_mkpl_header.get("addr_header"), vender_id=seller_id,
                                                                  date=date, sign='autotest')['object']['products']

            activity_tags = jmespath(all_products, '[*].activity_tag_list[0]')
            # tag 去除'Hot deals'
            activity_tags = [i for i in activity_tags if i != 'Hot deals']
            activity_tag = activity_tags[0]
            # 断言正则匹配 "Spend $xx+, get $xx off"
            pattern = r'Spend \$(\d+|\d+\.\d{1,2})\+, get \$(\d+|\d+\.\d{1,2}) off'
            assert re.match(pattern, activity_tag), f'activity_tag:{activity_tag} not match pattern'

    def test_seller_buy_x_get_x_off_apply(self, ec_mkpl_header):
        """
        验证商家已设置满减门槛活动加购达到门槛
        """

        if len(buy_x_get_x_off_seller) == 0:
            pytest.skip('No buy_x_get_x_off seller promotion ')
        else:
            promo_seller = buy_x_get_x_off_seller[0]
            seller_id = promo_seller['seller_id']
            all_products = SearchVender().vender_all_tab_products(headers=ec_mkpl_header.get("addr_header"), vender_id=seller_id,
                                                                  date=date, sign='autotest')['object']['products']

            tags = jmespath(all_products, '[*].activity_tag_list[0]')
            # tag 去除'Hot deals'
            activity_tag = [i for i in tags if i != 'Hot deals']
            activity_tag = activity_tag[0]
            # tag str 拆分promotion 门槛 promotion金额
            promo_min = float(activity_tag.split('$')[1].split('+')[0])
            promo_amount = float(activity_tag.split('get $')[1].split(' off')[0])
            available_products = [i for i in all_products if
                                  i['price'] * i['min_order_quantity'] < promo_min <= i['price'] * i[
                                      'max_order_quantity']]
            if len(available_products) == 0:
                pytest.skip('No available_products for seller promotion ')
            else:
                product = available_products[0]
                promo_min_qty = promo_min // product['price'] + 1
                # 清空购物车
                RemoveAllProductsInCart().clear_grocery_cart(headers=ec_mkpl_header.get("addr_header"))
                # mkpl 加购 金额低于coupon门槛
                cart_below = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"), product_id=product['id'],
                                                             qty=product['sales_min_order_quantity'],
                                                             seller_id=seller_id,
                                                             delivery_date=date, refer_type="seller")
                # 断言加购不到promotion 门槛 activity_save_amount为0
                cart_below_activity_save = cart_below['object']['seller_float_cart_list'][0]['fee_info'][
                    'activity_save_amount']
                assert cart_below_activity_save == '0.00', f'promo_tag{activity_tag},product_id:{product["id"]},price:{product["price"]}'
                cart_with_promotion = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"),
                                                                      product_id=product['id'],
                                                                      qty=promo_min_qty,
                                                                      seller_id=seller_id,
                                                                      delivery_date=date, refer_type="seller")
                cart_with_promotion_save = float(
                    cart_with_promotion['object']['seller_float_cart_list'][0]['fee_info']['activity_save_amount'])
                # 断言加购满门槛后 promotion apply
                assert cart_with_promotion_save == promo_amount, f'promo_tag{activity_tag},product_id:{product["id"]},price:{product["price"]}'

    def test_seller_buy_x_get_percent_off(self, ec_mkpl_header):
        """
        验证商家已设置满折门槛活动加购达到门槛
        """
        if len(percent_promotion_seller) == 0:
            pytest.skip('No buy_x_get_x_off seller promotion ')
        else:
            promo_seller = percent_promotion_seller[0]
            seller_id = promo_seller['seller_id']
            promo_tag = \
                [tag for tag in promo_seller['seller_tags'] if re.match(percent_promotion_pattern, tag['tag'])][0][
                    'tag']
            # tag str 拆分promotion 门槛 promotion金额
            promo_min = float(promo_tag.split('$')[1].split('+')[0])
            promo_percent = int(promo_tag.split('get ')[1].split('% off')[0])
            all_products = SearchVender().vender_all_tab_products(headers=ec_mkpl_header.get("addr_header"), vender_id=seller_id,
                                                                  date=date, sign='autotest')['object']['products']
            promo_products = []
            for product in all_products:
                price, min_qty = product['price'], product['sales_min_order_quantity']
                # 最大加购数量 对比库存和最大加购数量
                max_qty = min(product['sales_min_order_quantity'], product['remaining_count'])
                if price * min_qty < promo_min < price * max_qty:
                    promo_products.append(product)
            if len(promo_products) == 0:
                pytest.skip('No single product matches promo_min')
            else:
                promo_product = promo_products[0]
                promo_min_qty = promo_min // promo_product['price'] + 1
                # 清空购物车
                RemoveAllProductsInCart().clear_grocery_cart(headers=ec_mkpl_header.get("addr_header"))
                # mkpl 加购 金额低于coupon门槛
                cart_below = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"), product_id=product['id'],
                                                             qty=product['sales_min_order_quantity'],
                                                             seller_id=seller_id,
                                                             delivery_date=date, refer_type="seller")

                # 断言加购不到promotion 门槛 activity_save_amount为0
                cart_below_activity_save = cart_below['object']['seller_float_cart_list'][0]['fee_info'][
                    'activity_save_amount']
                assert cart_below_activity_save == '0.00', f'promo_tag{promo_tag},product_id:{product["id"]},price:{product["price"]}'
                cart_with_promotion = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"),
                                                                      product_id=product['id'],
                                                                      qty=promo_min_qty,
                                                                      seller_id=seller_id,
                                                                      delivery_date=date, refer_type="seller")
                cart_with_promotion_save = float(
                    cart_with_promotion['object']['seller_float_cart_list'][0]['fee_info']['activity_save_amount'])
                expect_promotion_save = promo_percent * promo_product['price'] * promo_min_qty / 100
                assert cart_with_promotion_save == expect_promotion_save, f'expect_promotion_save{expect_promotion_save},expect_promotion_save:{expect_promotion_save}'
