import weeeTest

from test_dir.api.ec.ec_social.seller_rest.getsellerpostlist import GetSellerPostList
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestGetSellerPostList(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'tb1', 'test_get_seller_post_list')
    def test_get_seller_video_post_list(self, ec_mkpl_header):
        """ # 商家主页视频  test_get_seller_post_list """
        seller_post_list = GetSellerPostList().get_seller_post_list(headers=ec_mkpl_header.get("addr_header"),
                                                                    seller_id=6887)
        assert seller_post_list["result"] is True, f'晒单数据异常{seller_post_list}'
        assert len(seller_post_list["object"]["list"]) > 0, f'晒单数据异常{seller_post_list}'
        for item in seller_post_list["object"]["list"]:
            CommonCheck().check_video_data(video_list=item)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
