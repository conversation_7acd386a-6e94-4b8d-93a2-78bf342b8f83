# !/usr/bin/python3
# -*- coding: utf-8 -*-

import json

import weeeTest

from test_dir.api.ec.ec_item.search_vender_rest.search_vender import SearchVender
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestSellerAllProducts(weeeTest.TestCase):
    def v3_search_vender(self, headers):
        search_vender = SearchVender().v3_search_vender(headers=headers,
                                                        filters=json.dumps({"catalogue_num": ""})
                                                        )

        assert search_vender["result"] is True
        assert search_vender["result"] is True, f'6887 商家页面all products数据异常{search_vender}'
        assert search_vender["object"]["total_count"] > 0, f'6887 商家页面all products数据异常{search_vender}'
        return search_vender

    @weeeTest.mark.list('Transaction', 'test_seller_all_products')
    def test_seller_all_products(self, ec_login_header):
        """ MKPL-商家页面all products验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 关键词搜索
        search_vender = self.v3_search_vender(headers=ec_login_header)
        categories = search_vender["object"]["categories"]
        filters = search_vender["object"]["filters"]
        products = search_vender["object"]["products"]
        sorts = search_vender["object"]["sorts"]
        vender_info_view = search_vender["object"]["vender_info_view"]
        venders = search_vender["object"]["venders"]
        assert venders[0]["vender_id"] == 6887
        assert venders[0]["vender_logo_url"] is not None

    @weeeTest.mark.list('Transaction', 'test_seller_all_products')
    def test_seller_all_products_category_filter(self, ec_login_header):
        """ MKPL-商家页面all products切换分类验证流程"""
        # 搜索结果切换分类
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 关键词搜索
        search_vender = self.v3_search_vender(headers=ec_login_header)
        categories = search_vender["object"]["categories"]
        for item in categories:
            search_vender = SearchVender().v3_search_vender(headers=ec_login_header,
                                                            filters=json.dumps({"catalogue_num": item["catalogue_num"]})
                                                            )

            assert search_vender["result"] is True, f'6887 商家页面all products 搜索{item["catalogue_num"]}数据异常{search_vender}'
            assert search_vender["object"][
                       "total_count"] > 0, f'6887 商家页面all products 搜索{item["catalogue_num"]}数据异常{search_vender}'
            assert search_vender["object"]["categories"]

    @weeeTest.mark.list('Transaction', 'test_seller_all_products')
    def test_seller_all_products_sort(self, ec_login_header):
        """ MKPL-商家页面all products sort排序验证流程"""
        # 搜索结果切换分类
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 关键词搜索
        search_keyword = self.v3_search_vender(headers=ec_login_header)
        sorts = search_keyword["object"]["sorts"]
        for item in sorts:
            cate_search = SearchVender().v3_search_vender(headers=ec_login_header,sort=item["sort_key"],
                                                            filters=json.dumps({"catalogue_num":""})
                                                            )
            assert cate_search["result"] is True, f'6887 商家页面all products 搜索{item["sort_key"]}数据异常{cate_search}'
            assert cate_search["object"]["total_count"] > 0, f'6887 商家页面all products 搜索{item["sort_key"]}数据异常{cate_search}'
            assert len(cate_search["object"]["products"]) > 0, f'6887 商家页面all products 搜索{item["sort_key"]}数据异常{cate_search}'
            # 对sort 进行断言
            CommonCheck().sort_assert(products=cate_search["object"]["products"], sort_key=item["sort_key"],
                                      category="global+")

    @weeeTest.mark.list('Transaction', 'test_seller_all_products')
    def test_seller_all_products_filter(self, ec_login_header):
        """ MKPL-商家页面all products filter筛选验证流程"""
        # 搜索结果切换分类
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 关键词搜索
        search_keyword = self.v3_search_vender(headers=ec_login_header)
        filters = search_keyword["object"]["filters"]
        for item in filters:
            property_key = item["property_key"]
            for item2 in item["property_values"]:
                value_key = item2["value_key"]
                cate_search = SearchVender().v3_search_vender(headers=ec_login_header,
                                                              filters=json.dumps({property_key: value_key}))

                assert cate_search["result"] is True, f'6887 商家页面all products 搜索{property_key}: {value_key}数据异常{cate_search}'
                assert cate_search["object"][
                           "total_count"] > 0, f'6887 商家页面all products 搜索{property_key}: {value_key}数据异常{cate_search}'
                assert len(
                    cate_search["object"][
                        "products"]) > 0, f'6887 商家页面all products 搜索{property_key}: {value_key}数据异常{cate_search}'

                CommonCheck().filter_assert(products=cate_search["object"]["products"],
                                            property_key=property_key, value_key=value_key, category="global+")

    @weeeTest.mark.list('Transaction', 'test_seller_all_products')
    def test_waterfall_search_product_check(self, ec_login_header):
        """ MKPL-商家页面all products 商品验证流程"""
        # 搜索结果切换分类
        # 关键词搜索
        search_keyword = self.v3_search_vender(headers=ec_login_header)
        products = search_keyword["object"]["products"]
        for item3 in products:
            CommonCheck().check_product_info(headers=ec_login_header, product=item3, category_type="global+")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
