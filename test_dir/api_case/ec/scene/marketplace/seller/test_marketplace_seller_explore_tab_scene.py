# !/usr/bin/python3
# -*- coding: utf-8 -*-

import pytest
import weeeTest
from weeeTest import weeeConfig, jmespath

from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_content.product_rest.query_product_list_by_ds_key import QueryProductListByDsKey
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestMarketpalceSellerExploreTab(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        """
        前置条件 登录加载all store 页面cms
        recommend_session：请求waterfall需要前端随机生成，自动化暂时使用AUTO-TEST+时间
        todo：pytest fixture
        :return:
        """
        # 参数对象全局，其他case中直接使用
        global zipcode, lang, sales_org_id, date
        # 调用方法生成 mkpl_common_headers zipcode sales_org_id date
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))
        zipcode, sales_org_id = common['zipcode'], common['sales_org_id']
        date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_store_page_explore_tab_cms(self, ec_mkpl_header):
        """
        商家explore tab cms 返回商品(production seller:6887)
        """
        seller_id = 6887
        explore_tab_cms = QueryPageData().query_page_data(headers=ec_mkpl_header.get("addr_header"), page_type=str(6),
                                                          page_key=str(6887), sales_org_id=sales_org_id,
                                                          zipcode=str(zipcode))
        components = explore_tab_cms['object']['layout']['sections'][0]['components']
        datasource = explore_tab_cms['object']['datasource']
        # 遍历components 根据对应的component.datasouce.[0] 找datasource中对应的ds_xxx.now
        for component in components:
            component_datasource = component['datasource'][0]
            for k, v in datasource.items():
                # ds_item_customize_xxx的组件 请求ec/content/product/list?dataobject_key=ds_item_customize_xxxx
                if component_datasource == k and k.startswith('ds_item_customize_'):
                    component_detail = QueryProductListByDsKey().query_product_list_by_ds_key(
                        headers=ec_mkpl_header.get("addr_header"), dataobject_key=k)
                    products = component_detail['object']['products']
                    for product in products:
                        # 断言 返回商品信息
                        CommonCheck().check_product_info(product=product, headers=ec_mkpl_header.get("addr_header"))


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
