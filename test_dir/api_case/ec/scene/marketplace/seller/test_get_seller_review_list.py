import weeeTest
from test_dir.api.ec.ec_social.seller_rest.getsellerreviewlist import GetSellerReviewList
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestGetSellerReviewList(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'tb1', 'test_seller_review_list')
    def test_seller_review_list(self, ec_mkpl_header):
        """# 商家主页，reviews,评论 test_seller_review """
        seller_review = GetSellerReviewList().seller_review(headers=ec_mkpl_header.get("addr_header"),
                                                            seller_id=6887)
        assert seller_review["result"] is True
        assert len(seller_review["object"]["list"]) > 0
        for item in seller_review["object"]["list"]:
            CommonCheck().check_social_review_list(review_list=item, source="seller")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
