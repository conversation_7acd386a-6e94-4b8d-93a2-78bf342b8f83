import pytest
import weeeTest
from weeeTest import weeeConfig, log, jmespath
from test_dir.api.ec.ec_so.mkpl_group_buy.create_group_shopping_cart import CreateGroupOrderCart
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import ListAllAvailableSellerIds
from test_dir.api.ec.ec_so.mkpl_group_buy.cancel_group_shopping_cart import CancelGroupShoppingCart
from test_dir.api.ec.ec_so.mkpl_group_buy.check_group_shopping_cart import  MkplGroupOrderQuery
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl



class TestCancelGroupOrder(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        _date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        res = ListAllAvailableSellerIds().list_all_available_seller_ids(
            headers=ec_mkpl_header.get("addr_header"),
            zipcode=ec_mkpl_header.get("zipcode")
        )
        seller_id = jmespath(res, 'object')[0]
        # seller all tab返回结果
        all_products = mkpl.SellerPage().all_product_tab(ec_mkpl_header.get("addr_header"), seller_id, _date)
        yield all_products, seller_id, _date
        pass

    @weeeTest.mark.list('singlecase',  'Transaction', 'product')
    def test_cancel_group_cart(self, setup, ec_mkpl_header):
        """
        Host用户先创建拼单 - 再发起取消
        """
        group_order_info = CreateGroupOrderCart().create_group_order(headers=ec_mkpl_header.get("addr_header"), vendor_id=setup[1])
        group_order_key = jmespath(group_order_info, 'object.key')
        cancel_group_order = CancelGroupShoppingCart().cancel_group_shopping_cart(headers=ec_mkpl_header.get("addr_header"), key=group_order_key)

        assert cancel_group_order['result'] == True
        assert cancel_group_order['traceId'] is not None and isinstance(cancel_group_order["traceId"], str), f'trace id返回异常，{group_order_key}'

        #取消拼团订单后，回到大购物车 & seller 主页 不会展示拼单banner入口信息
        group_order_info_update = MkplGroupOrderQuery().mkpl_group_buy(headers=ec_mkpl_header.get("addr_header"))

        assert group_order_info_update['object']['status'] == 1, f'拼单状态异常,{group_order_key}'
        assert group_order_info_update['object']['type'] == "none", f'拼单状态异常,{group_order_key}'
        assert group_order_info_update['object']['vendor_id'] is None, f'拼单状态异常,{group_order_key}'
        assert group_order_info_update['object']['vendor_name'] is None, f'拼单状态异常,{group_order_key}'
        assert group_order_info_update['object']['key'] is None, f'拼单状态异常,{group_order_key}'


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()







