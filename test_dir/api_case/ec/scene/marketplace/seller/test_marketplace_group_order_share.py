import pytest
import weeeTest
from weeeTest import weeeConfig, log, jmespath
from test_dir.api.ec.ec_so.mkpl_group_buy.create_group_shopping_cart import CreateGroupOrderCart
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import ListAllAvailableSellerIds
from test_dir.api.ec.ec_growth.share_group_buy import GroupOrderShare
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl

"""
MKPL拼单分享单接口-0312

Charlie Chen
"""

class TestGroupOrderShare(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_charlie_header):
        global zipcode, lang, sales_org_id, date, seller_id, all_products, group_order_info, group_order_key
        common = mkpl.Common().user_guest_params(headers=ec_charlie_header)
        zipcode, sales_org_id =  common['zipcode'], common['sales_org_id']
        date = mkpl.Common().porder_date(headers=ec_charlie_header)
        data = {"zipcode": str(zipcode)}
        res = ListAllAvailableSellerIds().list_all_available_seller_ids(headers=ec_charlie_header)
        seller_id = jmespath(res, 'object')[0]
        all_products = mkpl.SellerPage().all_product_tab(ec_charlie_header, seller_id, date)
        group_order_info = CreateGroupOrderCart().create_group_order(headers=ec_charlie_header,
                                                                     vendor_id=seller_id)
        group_order_key = jmespath(group_order_info, 'object.key')

    @weeeTest.mark.list('singlecase',  'Transaction')
    def test_group_order_share(self, ec_charlie_header):

        group_order_share  = GroupOrderShare().share_group_order(headers=ec_charlie_header, key=group_order_key, vendor_id=seller_id)
        print(group_order_share)

        assert self.response["result"] is True
        assert self.response["object"]["view_link"] is not None


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()






