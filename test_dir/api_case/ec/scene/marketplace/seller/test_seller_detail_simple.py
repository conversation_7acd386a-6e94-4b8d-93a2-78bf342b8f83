# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/19 09:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/19 09:06
"""
import weeeTest

from test_dir.api.ec.ec_marketplace.sellerinfo.seller_detail import VendorDetail
from test_dir.api.ec.ec_social.seller_rest.follow import Follow


class TestVendorDetail(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev', 'test_seller_detail')
    def test_seller_detail(self, ec_login_header):
        """
        #test_seller_desc 测试商家描述信息
        """
        vendor_id = 6887
        vendor_detail = VendorDetail().vendor_detail(headers=ec_login_header,
                                                     vendor_id=vendor_id)
        # 断言
        assert vendor_detail["result"] is True
        assert vendor_detail["object"]["vendor_id"] == vendor_id
        assert vendor_detail["object"]["title"] == "WOO JAPAN"
        assert vendor_detail["object"]["title_lang"] is not None
        assert vendor_detail["object"]["image_url"] is not None
        assert vendor_detail["object"]["overall_rating"] > 4
        assert vendor_detail["object"]["overall_rating_count"] > 300
        # 配送服务
        assert vendor_detail["object"]["delivery_rating"] > 4
        # 卖家服务
        assert vendor_detail["object"]["vendor_rating"] > 4
        # 配送时间范围
        assert vendor_detail["object"]["estimate_delivery_time"] is not None
        assert vendor_detail["object"]["estimate_delivery_time_from"] is not None
        assert vendor_detail["object"]["estimate_delivery_time_to"] is not None
        assert vendor_detail["object"]["latest_shipping_time"] is not None

        assert vendor_detail["object"]["estimate_reminder_content"] is not None
        assert vendor_detail["object"]["estimate_range_reminder_content"] is not None
        assert vendor_detail["object"]["shipping_reminder_content"] is not None
        assert vendor_detail["object"]["shipping_from_content"] is not None
        assert vendor_detail["object"]["reminder_contents"] is not None
        for item in vendor_detail["object"]["reminder_contents"]:
            assert item["key"] is not None
            assert item["title"] is not None
            assert item["title_full"] is not None
            assert item["icon_url"] is not None
        assert vendor_detail["object"]["sales_volume"] is not None
        assert vendor_detail["object"]["seller_url"] is not None
        assert "/mkpl/vendor/detail/" + str(vendor_id) in vendor_detail["object"]["seller_url"]
        assert vendor_detail["object"]["shipping_return_policy"] is not None
        # 关注状态
        assert vendor_detail["object"]["seller_follow_info"] is not None
        if vendor_detail["object"]["seller_follow_info"]["status"] == "C":
            # 去关注
            status = "A"
        else:
            # 取消关注
            status = "C"
        follow = Follow().seller_follow(headers=ec_login_header,
                                        seller_id=vendor_id,
                                        status=status)
        assert follow["result"] is True
        # 关于
        assert vendor_detail["object"]["description_html"] is not None
