# !/usr/bin/python3
# -*- coding: utf-8 -*-
import pytest
import weeeTest
from weeeTest import weeeConfig, jmespath

from test_dir.api.ec.ec_growth.share_vendor import ShareVendor
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import \
    ListAllAvailableSellerIds
from test_dir.api.ec.ec_marketplace.sellerinfo.seller_info import VendorInfo
from test_dir.api.ec.ec_social.seller_rest.follow import Follow
from test_dir.api.ec.ec_social.seller_rest.getsellerreviewlist import GetSellerReviewList
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestMarketpalceSeller(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        """
        前置条件 登录加载all store 页面cms
        recommend_session：请求waterfall需要前端随机生成，自动化暂时使用AUTO-TEST+时间
        todo：pytest fixture
        :return:
        """
        # 参数对象全局，其他case中直接使用
        global zipcode, sales_org_id, date, seller_id, user_id

        # 调用方法生成 mkpl_common_headers zipcode sales_org_id date
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))

        zipcode, sales_org_id =  common['zipcode'], common['sales_org_id']
        date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        # waterfall seller id
        # data = {"zipcode": str(zipcode)}
        res = ListAllAvailableSellerIds().list_all_available_seller_ids(headers=ec_mkpl_header.get("addr_header"),
                                                                        zipcode=str(zipcode))
        seller_id = jmespath(res, 'object')[0]

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_store_page_follow(self, ec_mkpl_header):
        """
        商家主页交互,点击follow按钮,关注商家，再次点击，取消关注[108513]
        """
        # 获取当前seller follow状态

        seller_info = VendorInfo().vendor_info(headers=ec_mkpl_header.get("addr_header"), vendor_id=seller_id)
        follow_status = jmespath(seller_info, 'object.seller_follow_info.status')
        status = ['A', 'C']
        status.remove(follow_status)
        change_status = status[0]
        # 修改follow状态
        Follow().seller_follow(headers=ec_mkpl_header.get("addr_header"), seller_id=seller_id, status=change_status)
        seller_info = VendorInfo().vendor_info(headers=ec_mkpl_header.get("addr_header"), vendor_id=seller_id)
        after_status = jmespath(seller_info, 'object.seller_follow_info.status')
        # 断言商家页follow 状态更新成功
        assert after_status == change_status

    # @weeeTest.mark.list('product')
    # def test_store_follow_sync_account_page(self):
    #     """
    #     商家主页交互,点击follow按钮,关注商家，再次点击，取消关注[108513]
    #     """
    #     # 获取当前seller follow状态
    #     seller_info = VendorInfo().vendor_info(headers=mkpl_common_headers, vendor_id=seller_id)
    #     follow_status = jmespath(seller_info, 'object.seller_follow_info.status')
    #     user_id = QuerySimplePreOrder().query_simple_preorder_v1(headers=mkpl_common_headers)['object']['user_id']
    #     # 如果未关注，关注商家
    #     if follow_status == 'C':
    #         Follow().seller_follow(headers=mkpl_common_headers, seller_id=seller_id, status='A')
    #     follow_count = \
    #         UserProfile().query_user_following_list_page(uid=str(user_id), headers=mkpl_common_headers)['object'][
    #             'total']
    #     # 预估关注页数，首屏超过10条 加载更多返回10条
    #     follow_page = follow_count // 10 + (follow_count % 10 != 0)
    #     follow_sellers = []
    #     for i in range(1, follow_page + 1):
    #         follow = UserProfile().query_user_following_list_page(uid=str(user_id), headers=mkpl_common_headers,
    #                                                               page=i)
    #         if follow['object']['list']:
    #             for following in follow['object']['list']:
    #                 if following['user_type'] == "seller":
    #                     follow_sellers.append(following['user_id'])
    #         i += 1
    #     # 断言follow的seller出现在账号关注中
    #     assert int(seller_id) in follow_sellers

    @weeeTest.mark.list('product')
    def test_seller_review_sort(self, ec_mkpl_header):
        """
         评论排序:按照评论时间倒序，最近得展示在上方
        """
        # 评论列表
        seller_review_recency = GetSellerReviewList().seller_page_review(ec_mkpl_header.get("addr_header"), seller_id, sort="recency")
        # 所有评论 rec_time
        review_rec_time_lst = jmespath(seller_review_recency, "object.list[*].rec_create_time")
        # 断言时间戳降序
        assert sorted(review_rec_time_lst, reverse=True)[0] == max(review_rec_time_lst)

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_seller_share(self, ec_mkpl_header):
        """
        商家分享,1，图片：商家logo 2，标题：商家名
        """
        seller_share_info = ShareVendor().share_vendor(headers=ec_mkpl_header.get("addr_header"), vendor_id=seller_id, tab="all",
                                                       biz_type="mkpl")
        assert seller_share_info['object']['show_preview'] is True, f'seller 分享信息返回异常，请确认{seller_share_info}'
        assert seller_share_info['object'][
                   'show_language'] is True, f'seller 分享信息返回异常，请确认{seller_share_info}'
        assert seller_share_info['object'][
                   'view_link'] is not None, f'seller 分享信息返回异常，请确认{seller_share_info}'

        for content in seller_share_info['object']['share_content']:
            assert "/mkpl/vendor" in content["link_url"], f'seller 分享信息返回异常，请确认{seller_share_info}'
            assert str(seller_id) in content["link_url"], f'seller 分享信息返回异常，请确认{seller_share_info}'
            assert content["share_img_url"] is not None, f'seller 分享信息返回异常，请确认{seller_share_info}'
            assert content["title"] is not None, f'seller 分享信息返回异常，请确认{seller_share_info}'
            assert content["link_url"] is not None, f'seller 分享信息返回异常，请确认{seller_share_info}'
            assert content["description"] is not None, f'seller 分享信息返回异常，请确认{seller_share_info}'
            assert content["language"] is not None, f'seller 分享信息返回异常，请确认{seller_share_info}'


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
