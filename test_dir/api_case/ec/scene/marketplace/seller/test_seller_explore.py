import json

import weeeTest

from test_dir.api.ec.ec_item.search_vender_rest.search_vender import SearchVender


class TestSellerExplore(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product-restore', 'dev')
    def test_v3_search_vender_new_arrival(self, *args, ec_login_header):
        """ test_v3_search_vender """
        # seller-new arrival
        search_vender = SearchVender().v3_search_vender(headers=ec_login_header,
                                                        vender_id=args[0]["seller_info"]["ids"][0],
                                                        biz_type=args[0]["biz_type"][0],
                                                        dataobject_key=args[0]["dataobject_key"][0],
                                                        filters=json.dumps({args[0]["biz_type"][0]: True})
                                                        )

        assert search_vender["result"] is True
        self.vender_explore_assert(search_vender["object"])

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev', 'test_v3_search_vender_best_sellers')
    def test_v3_search_vender_best_sellers(self, *args, ec_login_header):
        """ test_v3_search_vender """
        # seller-best sellers
        search_vender = SearchVender().v3_search_vender(headers=ec_login_header,
                                                        vender_id=args[0]["seller_info"]["ids"][0],
                                                        biz_type=args[0]["biz_type"][1],
                                                        dataobject_key=args[0]["dataobject_key"][1],
                                                        filters=json.dumps({args[0]["biz_type"][1]: True}))
        assert search_vender["result"] is True
        self.vender_explore_assert(search_vender["object"])

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev', 'test_v3_search_vender')
    def test_v3_search_vender_on_sale(self, *args, ec_login_header):
        """ test_v3_search_vender """
        # seller-on sale
        search_vender = SearchVender().v3_search_vender(headers=ec_login_header,
                                                        vender_id=args[0]["seller_info"]["ids"][0],
                                                        biz_type=args[0]["biz_type"][2],
                                                        dataobject_key=args[0]["dataobject_key"][2],
                                                        filters=json.dumps({args[0]["biz_type"][2]: True}))
        assert search_vender["result"] is True
        self.vender_explore_assert(search_vender["object"])

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev', 'test_v3_search_vender')
    def test_v3_search_vender_shop_more(self, *args, ec_login_header):
        """ test_v3_search_vender """
        # shop more from this shop
        search_vender = SearchVender().v3_search_vender_all(headers=ec_login_header,
                                                            vender_id=args[0]["seller_info"]["ids"][0],
                                                            biz_type=args[0]["biz_type"][3],
                                                            dataobject_key=args[0]["dataobject_key"][3],
                                                            )
        assert search_vender["result"] is True
        self.vender_explore_assert(search_vender["object"])

    def vender_explore_assert(self, search_vender):
        assert search_vender.get("total_count") > 0, f'search_vender'
        assert len(search_vender.get("categories")) > 0, f'search_vender'
        assert len(search_vender.get("filters")) > 0, f'search_vender'
        # assert len(search_vender["navigation"]) > 0
        assert len(search_vender.get("products")) > 0, f'search_vender'
