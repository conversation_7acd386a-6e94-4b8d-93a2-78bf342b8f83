# !/usr/bin/python3
# -*- coding: utf-8 -*-
import re

import pytest
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_marketplace.sellerinfo.seller_homepage import VendorHomePage
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestMarketpalceShippingFee(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        """
        前置条件 登录加载all store 页面cms
        recommend_session：请求waterfall需要前端随机生成，自动化暂时使用AUTO-TEST+时间
        todo：pytest fixture
        :return:
        """
        # 参数对象全局，其他case中直接使用
        global zipcode, sales_org_id, date

        # 调用方法生成 mkpl_common_headers zipcode sales_org_id date
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))
        zipcode, sales_org_id = common['zipcode'], common['sales_org_id']

    def test_shipping_fee_desc_free(self, ec_mkpl_header):
        """
        商家无门槛和运费时	展示免邮文案
        """
        # tb1 测试seller：
        #  免邮：  8095：2052217 <EMAIL>/552983
        tb1_seller_id = 8095
        desc = VendorHomePage().vendor_page_header(headers=ec_mkpl_header.get("addr_header"), vendor_id=tb1_seller_id)
        shipping_content = jmespath(desc, 'object.shipping_reminder_content')

        assert 'Free shipping from' in shipping_content

    def test_shipping_fee_fixed(self, ec_mkpl_header):
        """
        商家固定运费无免邮门槛	展示固定运费文案
        """
        # tb1 测试seller：
        #  固定运费无免邮门槛   8097     <EMAIL>/915031
        tb1_seller_id = 8097
        desc = VendorHomePage().vendor_page_header(headers=ec_mkpl_header.get("addr_header"), vendor_id=tb1_seller_id)
        shipping_content = jmespath(desc, 'object.shipping_reminder_content')
        # $x|x.xx shipping fee from Japan 正则匹配
        pattern = r'\$(\d+|\d+\.\d{1,2}) shipping fee from '
        # 断言正则匹配结果非空True
        assert re.match(pattern, shipping_content)

    def test_shipping_fee_threshold(self, ec_mkpl_header):
        """
        商家门槛免邮运费文案
        """
        # tb1 测试seller：
        # 门槛免邮和运费：8096 <EMAIL>/313952
        tb1_seller_id = 8096
        desc = VendorHomePage().vendor_page_header(headers=ec_mkpl_header.get("addr_header"), vendor_id=tb1_seller_id)
        shipping_content = jmespath(desc, 'object.shipping_reminder_content')
        # $19.9 or free shipping over $99 from Japan
        pattern = r'\$(\d+|\d+\.\d{1,2}) or free shipping over \$(\d+|\d+\.\d{1,2}) from '
        assert re.match(pattern, shipping_content)

