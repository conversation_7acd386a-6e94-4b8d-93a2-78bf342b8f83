# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestNormalCategoryFilterSortFilter(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_normal_category_filters_sort_filter(self, ec_mkpl_header):
        """ 普通分类下复选各sort+filter筛选验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_mkpl_header['addr_header'])["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 获取所有一级普通分类,不包括特殊分类
        ApiCatalogues().catalogue_home(headers=ec_mkpl_header['addr_header'])
        assert self.response["result"] is True
        category_list = self.response["object"]["category_list"]
        # 根据获取到的一级分类，在访问对应的普通分类
        for category in category_list:
            if category["type"] != 0 or category["num"] in ["global"]:
                continue
            # 访问普通分类
            SearchByCatalogueContent().search_by_catalogue_content(headers=ec_mkpl_header['addr_header'], date=delivery_date,
                                                                   filter_sub_category=category["num"])
            assert self.response["result"] is True
            # 断言每个分类下必须要有数据
            assert self.response["object"]["total_count"] > 0

            # 获取分类下的filters 与sorts
            filters = self.response["object"]["filters"]
            sorts = self.response["object"]["sorts"]
            contents = self.response["object"]["contents"]
            # 单选各sort筛选验证流程
            for sort in sorts:
                # 精选、热销、价格：低到高、价格：高到底
                SearchByCatalogueContent().search_by_catalogue_content(headers=ec_mkpl_header['addr_header'], date=delivery_date,
                                                                       filter_sub_category=category["num"],
                                                                       sort=sort["sort_key"])

                # 断言每个分类下必须要有数据
                assert self.response["object"]["total_count"] > 0
                assert self.response["result"] is True
                # 进行filter 筛选
                for filter in filters:
                    # 价格、产地、商家不在此过滤
                    if filter["property_key"] in ["6", "5", "8"]:
                        continue
                    SearchByCatalogueContent().search_by_catalogue_content(headers=ec_mkpl_header['addr_header'], date=delivery_date,
                                                                           filter_sub_category=category["num"],
                                                                           sort=sort["sort_key"],
                                                                           filters={filter["property_key"]: True})

                    # 断言每个分类下必须要有数据
                    # assert self.response["object"]["total_count"] > 0
                    assert self.response["result"] is True

            # 循环一次就行

            break


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
