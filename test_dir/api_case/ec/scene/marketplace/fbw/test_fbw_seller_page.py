import requests
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_marketplace.sellerinfo.seller_homepage import VendorHomePage
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestFbwLandPageScene(weeeTest.TestCase):
    """
    FBW landing  page
    """

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_fbw_seller_info(self, ec_mkpl_header):
        """
        fbw seller page info
        """
        header, zipcode, sales_org_id, date = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id'], ec_mkpl_header['date']
        fbw_bakery_page_cms = QueryPageData().query_page_data(headers=header, page_key='bakery',
                                                              page_type='8',
                                                              lang='en',
                                                              sales_org_id=sales_org_id, zipcode=zipcode)
        # cms页面components
        components = fbw_bakery_page_cms['object']['layout']['sections'][0]['components']
        all_bakery_feed_component = \
            [component for component in components if component['component_instance_key'] == 'cm_content_feed_v2'][0]
        all_bakery_content_feed_component_ds = all_bakery_feed_component['datasource'][0]
        all_bakery_content_feed_component_link = \
            fbw_bakery_page_cms['object']['datasource']['{}'.format(all_bakery_content_feed_component_ds)]['now']
        all_bakery_detail = mkpl.Common().fbw_ld_detail(header=header,
                                                        url=all_bakery_content_feed_component_link,
                                                        dataobject_key=all_bakery_content_feed_component_ds)
        # 取第一个商品的vender id
        fbw_seller_id = jmespath(all_bakery_detail, 'object.contents[0].data.product.vender_id')
        # fbw seller 信息
        fbw_seller_info = VendorHomePage().vendor_page_header(headers=header, vendor_id=fbw_seller_id,
                                                              biz_type='fbw')
        # 图片链接请求
        img_url = fbw_seller_info['object']['image_url']
        # 断言 type为fbw,logo图片链接有效
        response = requests.get(img_url)
        assert response.status_code == 200 and fbw_seller_info['object'][
            'seller_type'] == 'fbw', f'img status_code:{response.status_code},return fbw seller type{fbw_seller_info}'
