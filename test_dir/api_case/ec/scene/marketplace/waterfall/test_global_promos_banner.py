# !/usr/bin/python3
# -*- coding: utf-8 -*-
import weeeTest

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_promos import Globalpromos
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestGlobalPromosSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'test_global_promos')
    def test_global_promos(self, ec_login_header):
        """  # Waterfall页面Banner验证流程 """
        # 获取waterfall 页面banner
        global_promo = Globalpromos().global_promos(headers=ec_login_header)
        assert global_promo["result"] is True, f'Waterfall页面Banner数据异常{global_promo}'
        assert len(global_promo.get("object").get("data")) == 4, f'Waterfall页面Banner数据异常{global_promo}'
        for item in global_promo.get("object").get("data"):
            assert item.get("image_url") is not None, f'Waterfall页面Banner数据异常{global_promo}'
            assert item.get("key") is not None, f'Waterfall页面Banner数据异常{global_promo}'
            assert item.get("link_url") is not None, f'Waterfall页面Banner数据异常{global_promo}'
            assert item.get("type") is not None, f'Waterfall页面Banner数据异常{global_promo}'
            if item.get("key") == "mkpl_banner_1":
                # 第一和第三的banner 会根据需求改变
                pass
            elif item.get("key") == "mkpl_banner_2":
                assert "/mkpl/top-items" in item.get("link_url"), f'Waterfall页面Banner数据异常{global_promo}'
            elif item.get("key") == "mkpl_banner_3":
                pass
            elif item.get("key") == "mkpl_banner_4":
                assert "/zh/mkpl/global" in item.get("link_url"), f'Waterfall页面Banner数据异常{global_promo}'

            # 点击跳转
            CommCheckFunction().comm_check_link(item.get("link_url"), headers=ec_login_header)
            # 断言某个key 一定存在
            assert any(item1['key'] == "mkpl_banner_4" for item1 in
                       global_promo.get("object").get("data")), "data 中不存在 key 为 'mkpl_banner_3' 的项"

        # # 断言banner 返回行数
        # if len(global_promo.get("object").get("data")) <= 2:
        #     assert global_promo.get("object").get("rows") == 1, f'Waterfall页面Banner数据异常{global_promo}'
        # else:
        #     assert global_promo.get("object").get("rows") == 2, f'Waterfall页面Banner数据异常{global_promo}'

