# !/usr/bin/python3
# -*- coding: utf-8 -*-
import requests
import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.get_global_welcome_coupons import GetGlobalWelcomeCoupons
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check_mkpl import WelcomeCouponCheck


class TestGetGlobalWelcomeCoupons(weeeTest.TestCase):

    @weeeTest.mark.list('welcome_coupons_all', 'Regression',  'Transaction')
    def test_get_global_welcome_coupons_all(self, ec_login_header):
        """
        mkpl列表新人优惠券信息

        """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # waterfall页面的优惠券列表
        welcome_couple = GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=ec_login_header,
                                                             zipcode=porder["zipcode"],
                                                             source_key="global_waterfall", tab_key=None)

        log.info("get_global_welcome_coupons===>", welcome_couple)
        # 29行assert曾经报错，加入调试信息
        assert welcome_couple["result"] is True, f'welcome coupon res:{self.response}'
        if welcome_couple["object"]["coupons"] is not None:
            WelcomeCouponCheck.all_welcome_coupons(welcome_couple, headers=ec_login_header)
        if welcome_couple["object"] is not None:
            coupons = welcome_couple["object"]["coupons"]
            tabs = welcome_couple["object"]["tabs"]
            # 访问优惠券列表页面
            url = welcome_couple["object"]['link']
            response = requests.get(url)
            if response.status_code == 200:
                print(f"{url} is accessible")
            else:
                print(f"{url} is not accessible")

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_get_global_welcome_coupons_landing_page_fou_you(self, ec_login_header):
        """
        mkpl coupon landing页面的优惠券列表 fou you

        """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # landing page 页面的优惠券列表
        GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=ec_login_header,
                                                             zipcode=porder["zipcode"],
                                                             source_key="global_waterfall", tab_key="all")
        assert self.response["result"] is True
        if self.response["object"]["coupons"] is not None:
            WelcomeCouponCheck.all_welcome_coupons(self.response, headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_get_global_welcome_coupons_landing_page_expiring_soon(self, ec_login_header):
        """
        mkpl coupon landing页面的优惠券列表 expiring_soon,7天内过期优惠

        """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # landing page 页面的优惠券列表
        GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=ec_login_header,
                                                             zipcode=porder["zipcode"],
                                                             source_key="global_waterfall", tab_key="expiring_soon")
        assert self.response["result"] is True
        if self.response["object"]["coupons"] is not None:
            WelcomeCouponCheck.all_welcome_coupons(self.response, headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_get_global_welcome_coupons_landing_page_claimed(self, ec_login_header):
        """
        mkpl coupon landing页面的优惠券列表 claimed，已领取的优惠券

        """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # landing page 页面的优惠券列表
        GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=ec_login_header,
                                                             zipcode=porder["zipcode"],
                                                             source_key="global_waterfall", tab_key="claimed")
        assert self.response["result"] is True
        if self.response["object"]["coupons"] is not None:
            WelcomeCouponCheck.all_welcome_coupons(self.response, headers=ec_login_header)

