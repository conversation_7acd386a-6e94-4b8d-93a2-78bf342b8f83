import weeeTest

from test_dir.api.ec.ec_item.search_vender_rest.categories_global import CategoriesGlobal


class TestCategoriesGlobal(weeeTest.TestCase):
    # 获取waterfall的分类
    @weeeTest.mark.list('Transaction', 'product', 'test_global_categories')
    def test_global_categories(self, pc_header):
        global_categories = CategoriesGlobal().categories_global(headers=pc_header)
        assert global_categories["result"] is True, f'global 分类数据异常{global_categories}'
        category_lists = global_categories.get("object").get("category_list")
        assert len(category_lists) > 0, f'global 分类数据异常{global_categories}'
        # 对global页面返回的分类进行断言
        for item in category_lists:
            self.global_categories_assert(item)

    def global_categories_assert(self, category):
        assert category.get("key") is not None, f'global 分类数据异常{category}'
        assert category.get("name") is not None, f'global 分类数据异常{category}'
        assert category.get("num") is not None, f'global 分类数据异常{category}'
        assert category.get("thumbnail_img_url") is not None, f'global 分类数据异常{category}'
        assert category.get("url") is not None, f'global 分类数据异常{category}'
        if category.get("key") == "all_store":
            assert "/mkpl/global" in category.get("url")
        else:
            pass
            # assert "" in category.get("url")
        # assert type(category.get["is_new"]) == bool, f'global 分类数据异常{category}'
