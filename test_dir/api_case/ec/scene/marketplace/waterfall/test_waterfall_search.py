# !/usr/bin/python3
# -*- coding: utf-8 -*-

import json

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_keyword_v3 import SearchByKeywordV3
from test_dir.api.ec.ec_search.ec_search import EcSearch
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestWaterfallSearch(weeeTest.TestCase):
    def ec_search_keyword(self, headers):
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        # 关键词搜索
        search_keyword = EcSearch().ec_search_keyword(headers=headers,
                                                      from_page="mkpl_global_search",
                                                      zipcode=porder["zipcode"],
                                                      date=porder["delivery_pickup_date"],
                                                      lang="en", filter_key_word='fruit',
                                                      trigger_type="search_active",
                                                      )
        assert search_keyword["result"] is True, f'waterfall 搜索数据异常{search_keyword}'
        assert search_keyword["object"]["total_count"] > 0, f'waterfall 搜索数据异常{search_keyword}'
        return search_keyword

    @weeeTest.mark.list('Transaction', 'test_waterfall_search')
    def test_waterfall_search_key_word(self, ec_login_header):
        """ MKPL-首页waterfall搜索验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 关键词搜索
        search_keyword = self.ec_search_keyword(headers=ec_login_header)
        navigation = search_keyword["object"]["navigation"]
        products = search_keyword["object"]["products"]
        venders = search_keyword["object"]["venders"]

    @weeeTest.mark.list('Transaction', 'test_waterfall_search')
    def test_waterfall_search_history(self, ec_login_header):
        """ MKPL-首页waterfall搜索历史词验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 历史词搜索
        search_keyword = EcSearch().ec_search_keyword(headers=ec_login_header,
                                                      from_page="mkpl_global_search",
                                                      zipcode=porder["zipcode"],
                                                      date=porder["delivery_pickup_date"],
                                                      lang="en", filter_key_word='fruit',
                                                      trigger_type="search_history",
                                                      )
        # result1 = V3Search().v3_search_history(headers=RequestHeader.ec_login_header, filter_key_word='fruit',
        #                                        lang="en",
        #                                        date=porder["delivery_pickup_date"]
        #                                        , zipcode=porder["zipcode"])
        assert search_keyword["result"] is True, f'waterfall 搜索数据异常{search_keyword}'
        assert search_keyword["object"]["total_count"] > 0, f'waterfall 搜索数据异常{search_keyword}'

    @weeeTest.mark.list('Transaction', 'test_waterfall_search')
    def test_waterfall_search_category_filter(self, ec_login_header):
        """ MKPL-首页waterfall搜索结果切换分类验证流程"""
        # 搜索结果切换分类
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 关键词搜索
        search_keyword = self.ec_search_keyword(headers=ec_login_header)
        categories = search_keyword["object"]["categories"]
        for item in categories:
            cate_search = EcSearch().ec_search_keyword(headers=ec_login_header,
                                                       from_page="mkpl_global_search",
                                                       zipcode=porder["zipcode"],
                                                       date=porder["delivery_pickup_date"],
                                                       lang="en", filter_key_word='fruit',
                                                       trigger_type="taxonomy_" + item["catalogue_num"],
                                                       filters=json.dumps(
                                                                  {"catalogue_num": item["catalogue_num"]})
                                                       )
            assert cate_search["result"] is True, f'waterfall 搜索{item["catalogue_num"]}数据异常{cate_search}'
            assert cate_search["object"][
                       "total_count"] > 0, f'waterfall 搜索{item["catalogue_num"]}数据异常{search_keyword}'
            assert cate_search["object"]["categories"]

    @weeeTest.mark.list('Transaction', 'test_waterfall_search')
    def test_waterfall_search_sort(self, ec_login_header):
        """ MKPL-首页waterfall搜索结果sort排序验证流程"""
        # 搜索结果切换分类
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 关键词搜索
        search_keyword = self.ec_search_keyword(headers=ec_login_header)
        sorts = search_keyword["object"]["sorts"]
        for item in sorts:
            cate_search = EcSearch().ec_search_keyword(headers=ec_login_header,
                                                       from_page="mkpl_global_search",
                                                       zipcode=porder["zipcode"],
                                                       date=porder["delivery_pickup_date"],
                                                       lang="en", filter_key_word='fruit',
                                                       trigger_type="filter",
                                                       sort=item["sort_key"]
                                                       )
            assert cate_search["result"] is True, f'waterfall 搜索{item["sort_key"]}数据异常{cate_search}'
            assert cate_search["object"]["total_count"] > 0, f'waterfall 搜索{item["sort_key"]}数据异常{cate_search}'
            assert len(cate_search["object"]["products"]) > 0, f'waterfall 搜索{item["sort_key"]}数据异常{cate_search}'
            # 对sort 进行断言
            CommonCheck().sort_assert(products=cate_search["object"]["products"], sort_key=item["sort_key"],
                                      category="global+")

    @weeeTest.mark.list('Transaction', 'test_waterfall_search')
    def test_waterfall_search_filter(self, ec_login_header):
        """ MKPL-首页waterfall搜索结果filter筛选验证流程"""
        # 搜索结果切换分类
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 关键词搜索
        search_keyword = self.ec_search_keyword(headers=ec_login_header)
        filters = search_keyword["object"]["filters"]
        for item in filters:
            property_key = item["property_key"]
            for item2 in item["property_values"]:
                value_key = item2["value_key"]
                cate_search = EcSearch().ec_search_keyword(headers=ec_login_header,
                                                           from_page="mkpl_global_search",
                                                           zipcode=porder["zipcode"],
                                                           date=porder["delivery_pickup_date"],
                                                           lang="en", filter_key_word='fruit',
                                                           trigger_type="filter",
                                                           filters=json.dumps({property_key: value_key})
                                                           )
                assert cate_search["result"] is True, f'waterfall 搜索{property_key}: {value_key}数据异常{cate_search}'
                assert cate_search["object"][
                           "total_count"] > 0, f'waterfall 搜索{property_key}: {value_key}数据异常{cate_search}'
                assert len(
                    cate_search["object"][
                        "products"]) > 0, f'waterfall 搜索{property_key}: {value_key}数据异常{cate_search}'

                CommonCheck().filter_assert(products=cate_search["object"]["products"],
                                            property_key=property_key, value_key=value_key, category="global+")

    @weeeTest.mark.list('Transaction', 'test_waterfall_search')
    def test_waterfall_search_product_check(self, ec_login_header):
        """ MKPL-首页waterfall搜索结果商品验证流程"""
        # 搜索结果切换分类
        # 关键词搜索
        search_keyword = self.ec_search_keyword(headers=ec_login_header)
        products = search_keyword["object"]["products"]
        for item3 in products:
            CommonCheck().check_product_info(ec_login_header, product=item3, category_type="global+")

    @weeeTest.mark.list('mkpl_waterfall_search', 'Regression', 'Smoke', 'Transaction')
    def test_mkpl_waterfall_search_v3(self, ec_login_header):
        """ mkpl搜索-waterfall页面搜索验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        search_res = SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header,
                                                              filter_key_word="tofu", lang="en",
                                                              from_page="mkpl_global_search",
                                                              zipcode=porder["zipcode"],
                                                              trigger_type="search_history",
                                                              date=porder["delivery_pickup_date"])
        # 断言搜索结果必有产品
        assert search_res["object"]["total_count"] > 0

        assert all(
            product["is_mkpl"] for product in search_res["object"]["products"]), f'Not all products.is_mkpl are True'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
