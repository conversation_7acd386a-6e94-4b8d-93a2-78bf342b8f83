# !/usr/bin/python3
# -*- coding: utf-8 -*-

import uuid

import requests
import weeeTest

from test_dir.api.ec.ec_content.global_rest.global_feed import GlobalFeed

recommend_session = str(uuid.uuid4())


class TestWaterfallBanners(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_global_promos_check(self, ec_login_header):
        """  # Waterfall页面Feature的Banner验证流程 """

        # 获取waterfall 页面banner
        global_feed = GlobalFeed().global_feed(headers=ec_login_header,
                                               recommend_session=recommend_session)
        assert global_feed["result"] is True
        assert global_feed["object"]["contents"], f"global_feed={global_feed}"
        contents = global_feed["object"]["contents"]
        # 找到 type 为 "banners" 的内容
        banners_content = next((item for item in contents if item["type"] == "banners"), None)

        # 断言 banners_content 存在
        assert banners_content is not None, "No content with type 'banners' found"

        # 断言 banners_content 中的 carousel 长度大于 0
        assert len(banners_content["data"]["banners"]["carousel"]) > 0, "Carousel length is not greater than 0"

        if self.response["object"]["contents"][0]["type"] == "banners":
            data = self.response["object"]["contents"][0]["data"]["banners"]["carousel"]
            for banner in data:
                # 验证点击banner跳转正常
                url = banner['url']
                response = requests.get(url)
                if response.status_code == 200:
                    print(f"{url} is accessible")
                else:
                    print(f"{url} is not accessible")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
