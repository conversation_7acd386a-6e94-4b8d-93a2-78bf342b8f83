# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from test_dir.api_case.ec.common.common_check_mkpl import BannerArrayCheck
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestGlobalBannerArray(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_get_global_banner_array_qty(self, ec_mkpl_header):
        """
        waterfall Banner验证: banner 数据link可用，Banner只展示两个或四个

        """

        # 登录获取第一个地址对应zipcode sales_org 更新header
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))

        _date, zipcode, sales_org_id =  common['date'], common['zipcode'], common['sales_org_id']
        # 获取首页配置
        global_cms_config = mkpl.GlobalPlusPage().cms_config(headers=ec_mkpl_header.get("addr_header"), zipcode=zipcode,
                                                             sales_org_id=sales_org_id,
                                                             lang='en')
        # 获取banner_array key
        cm_banner_array = mkpl.GlobalPlusPage().cms_key_component(global_cms_config, 'cm_banner_array')
        # 获取banner_array 详情
        banner_array_detail = mkpl.GlobalPlusPage().cms_component_detail(ec_mkpl_header.get("addr_header"), cm_banner_array)
        BannerArrayCheck.all_promo_banners(banner_array_detail, headers=ec_mkpl_header.get("addr_header"))


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
