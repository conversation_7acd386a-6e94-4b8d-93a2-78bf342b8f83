# !/usr/bin/python3
# -*- coding: utf-8 -*-

import uuid

import weeeTest

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.feed import MkplWaterfallFeed
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestGlobalWaterfall(weeeTest.TestCase):
    def marketplace_global_feed(self, headers):
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        # 关键词搜索
        global_feed = MkplWaterfallFeed().marketplace_global_feed(headers=headers,
                                                                  recommend_session=str(uuid.uuid4()))
        assert global_feed["result"] is True, f'global+waterfall 搜索数据异常{global_feed}'
        assert len(global_feed["object"]["contents"]) > 0, f'global+waterfall 搜索数据异常{global_feed}'
        assert len(global_feed["object"]["tabs"]) > 0, f'global+waterfall 搜索数据异常{global_feed}'

        return global_feed

    @weeeTest.mark.list('Transaction', 'test_global_waterfall_contents')
    def test_global_waterfall(self, ec_login_header):
        """ MKPL-首页waterfall验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 关键词搜索
        global_feed = self.marketplace_global_feed(headers=ec_login_header)
        contents = global_feed["object"]["contents"]
        tabs = global_feed["object"]["tabs"]

    @weeeTest.mark.list('Transaction', 'test_global_waterfall_contents')
    def test_global_waterfall_tab(self, ec_login_header):
        """ MKPL-首页waterfall切换分类tab验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        global_feed = self.marketplace_global_feed(headers=ec_login_header)
        tabs = global_feed["object"]["tabs"]
        for item in tabs:
            assert item["event_key"] is not None, f'global+waterfall 搜索数据异常{global_feed}'
            assert item["key"] is not None, f'global+waterfall 搜索数据异常{global_feed}'
            assert item["title"] is not None, f'global+waterfall 搜索数据异常{global_feed}'
            global_tab = MkplWaterfallFeed().marketplace_global_feed(headers=ec_login_header,
                                                                     recommend_session=str(uuid.uuid4()),
                                                                     key=item["key"])

            assert global_tab["result"] is True, f'global+waterfall 搜索数据异常{global_feed}'
            assert len(global_tab["object"]["contents"]) > 0, f'global+waterfall 搜索数据异常{global_feed}'
            assert len(global_tab["object"]["tabs"]) > 0, f'global+waterfall 搜索数据异常{global_feed}'

    @weeeTest.mark.list('Transaction', 'test_global_waterfall_contents')
    def test_global_waterfall_contents(self, ec_login_header):
        """ MKPL-首页waterfall返回卡片内容验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        global_feed = self.marketplace_global_feed(headers=ec_login_header)
        contents = global_feed["object"]["contents"]
        for item in contents:
            self.waterfall_contents_assert(content=item, type=item["type"], source="global+", headers=ec_login_header)

    def waterfall_contents_assert(self, content, type, source, headers):
        if type == "seller":
            seller = content["data"]["seller"]
            assert seller["description"] is not None, f'{source}:waterfall 搜索数据异常{content}'
            assert seller["logo_url"] is not None, f'{source}:waterfall 搜索数据异常{content}'
            assert seller["id"] is not None, f'{source}:waterfall 搜索数据异常{content}'
            assert seller["seller_url"] is not None, f'{source}:waterfall 搜索数据异常{content}'
            assert "/mkpl/vendor/" + str(seller["id"]) in seller[
                "seller_url"], f'{source}:waterfall 搜索数据异常{content}'
            assert seller["tags"] is not None, f'{source}:waterfall 搜索数据异常{content}'
            assert seller["title"] is not None, f'{source}:waterfall 搜索数据异常{content}'
            assert seller["products"] is not None, f'{source}:waterfall 搜索数据异常{content}'
            for item in seller["products"]:
                assert item["image_url"], f'{source}:waterfall 搜索数据异常{content}'
                assert item["price"], f'{source}:waterfall 搜索数据异常{content}'
                assert item["title"], f'{source}:waterfall 搜索数据异常{content}'
                if item["base_price"]:
                    assert item["base_price"] > item["price"], f'{source}:waterfall 搜索数据异常{content}'
        elif type == "item":
            product = content["data"]["product"]
            # 验证商品
            for index, item in enumerate(product):
                CommonCheck().check_product_info(product=item, category_type=source, headers=headers)
                if index == 2:
                    break
        elif type == "banners":
            banners = content["data"]["banners"]["carousel"]
            for banner in banners:
                # 断言这个链接就是视频review的链接
                assert banner["url"] is not None, f"waterfall里返回的banner信息{banner}"
                assert banner["img"] is not None, f"waterfall里返回的banner信息{banner}"
                assert banner["id"] is not None, f"waterfall里返回的banner信息{banner}"
                assert banner["pos"] is not None, f"waterfall里返回的banner信息{banner}"
                assert banner["status"] == "A", f"waterfall里返回的banner信息{banner}"
                if source == "global+":
                    assert banner["biz_type"] == "global_plus_banner_group_A", f"waterfall里返回的banner信息{banner}"
                    assert banner["type"] == "global_plus", f"waterfall里返回的banner信息{banner}"
                # 点击banner
                CommCheckFunction().comm_check_link(banner["url"], headers=headers)
        elif type == "video":
            post = content["data"]["post"]
            CommonCheck().check_video_data(post, headers=headers)
        elif type == "normal_content_video":
            post = content["data"]["post"]
            CommonCheck().check_video_data(post, headers=headers)
        elif type == "recipe_content_video":
            post = content["data"]["post"]
            CommonCheck().check_video_data(post, headers=headers)
        elif type == "cm_theme":
            # todo 抽取公共方法
            assert content["data"]["wf_theme"]["title"] is not None
            assert content["data"]["wf_theme"]["more_link_title"] is not None
            assert content["data"]["wf_theme"]["vertical_img"] is not None
            more_link = content["data"]["wf_theme"]["more_link"]
            assert "/promotion/theme_landing" in content["data"]["wf_theme"]["more_link"]
            # 点击查看更多进主题详情
            CommCheckFunction().comm_check_link(more_link, headers=headers)
            assert content["data"]["wf_theme"]["product_imgs"] is not None
            assert content["data"]["wf_theme"]["product_ids"] is not None
        elif type == "wf_collection":
            # todo 抽取公共方法
            more_link = content["data"]["wf_collection"]["more_link"]
            products = content["data"]["wf_collection"]["products"]
            # 点击查看更多进入合集详情
            CommCheckFunction().comm_check_link(more_link, headers=headers)
            assert products, f"当前wf_collection没有商品，contents为：{content}"
            assert content["data"]["wf_collection"]["title"] is not None
            assert content["data"]["wf_collection"]["more_link_title"] is not None
            assert content["data"]["wf_collection"]["type"] is not None
            if content["data"]["wf_collection"]["is_mkpl_collection"] is False:
                # 手工合集 or cms 合集
                assert content["data"]["wf_collection"]["type"] == "manual_collection"
            elif content["data"]["wf_collection"]["is_mkpl_collection"] is True:
                # mkpl 合集
                assert content["data"]["wf_collection"]["type"] == "mkpl_collection"
                assert all(product.get('biz_type') == "seller" for product in
                           products), "Not all biz_type values are 'seller'"
        elif type == "wf_collection_v2":
            # todo 抽取公共方法
            more_link = content["data"]["wf_collection_v2"]["more_link"]
            # 点击查看更多进入合集详情
            CommCheckFunction().comm_check_link(more_link, headers=headers)
            assert content["data"]["wf_collection_v2"]["title"] is not None
            assert content["data"]["wf_collection_v2"]["more_link_title"] is not None
            assert content["data"]["wf_collection_v2"]["type"] is not None
            assert content["data"]["wf_collection_v2"]["collection_type"] == "theme"
            assert content["data"]["wf_collection_v2"]["business_type"] == "mkpl"

            if content["data"]["wf_collection_v2"]["type"] == "topx":
                assert "/promotion/top-x/" in content["data"]["wf_collection_v2"]["more_link"]

            products = content["data"]["wf_collection_v2"]["products"]
            assert len(products) == 4, f"当前wf_collection_v2没有商品，contents为：{content}"
            assert products, f"当前wf_collection_v2没有商品，contents为：{content}"
        elif type == "wf_list":
            # todo 抽取公共方法
            more_link = content["data"]["wf_list"]["more_link"]
            # 点击查看更多进入合集详情
            CommCheckFunction().comm_check_link(more_link, headers=headers)
            assert content["data"]["wf_list"]["title"] is not None, f'waterfall 内容{content}'
            assert content["data"]["wf_list"]["more_link_title"] is not None, f'waterfall 内容{content}'
            assert content["data"]["wf_list"]["type"] is not None, f'waterfall 内容{content}'
            assert content["data"]["wf_list"]["type"] == content["data"]["wf_list"][
                "refer_id"], f'waterfall 内容{content}'
            if content["data"]["wf_list"]["type"] == "cm_item_new":
                # 新品上架
                assert "/category/new?filter_sub_category=new" in more_link, f'waterfall 内容{content}'
            elif content["data"]["wf_list"]["type"] == "cm_item_fbw_bakery":
                # 每日现做
                assert "/promotion/collection/fbwbakerycollection" in more_link, f'waterfall 内容{content}'
            elif content["data"]["wf_list"]["type"] == "cm_item_exposure_collection":
                # 发现好货
                assert "/promotion/collection/exposurecollection" in more_link, f'waterfall 内容{content}'
            elif content["data"]["wf_list"]["type"] == "cm_item_buy_it_again":
                # 曾经购买
                assert "/account/my-list?type=bought" in more_link, f'waterfall 内容{content}'
        elif type == "cm_lightning_deals":
            more_link = content["data"]["lightning_deals"]["more_link"]
            # 断言这个链接就是秒杀详情的链接
            assert "lightning-deals" in more_link, f"这个链接返回的不是秒杀详情链接，请确认.链接为：{more_link}"
            # 点击Buy now进入秒杀详情
            CommCheckFunction().comm_check_link(more_link, headers=headers)

            # 点击秒杀title也是进入秒杀详情
            detail_more_link = content["data"]["lightning_deals"]["component_metadata"]["more_link"]
            # 点击title进入秒杀详情
            CommCheckFunction().comm_check_link(detail_more_link, headers=headers)
