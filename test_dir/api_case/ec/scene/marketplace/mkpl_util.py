# !/usr/bin/python3
# -*- coding: utf-8 -*-

import datetime
from typing import Optional
import copy

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_content.global_rest.global_feed import GlobalFeed
from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_item.search_vender_rest.search_vender import SearchVender
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.get_global_welcome_coupons import GetGlobalWelcomeCoupons
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_lightning import GlobalLightning
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_origin_list import Globaloriginlist
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_promos import Globalpromos
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_store_all import GlobalStore
from test_dir.api.ec.ec_marketplace.search.mkpl_search import MkplSearch
from test_dir.api.ec.ec_marketplace.sellerinfo.seller_homepage import VendorHomePage
from test_dir.api.ec.ec_mkt.banner.global_trending_list import QueryTrendingListByDsKey
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.seller.query_mkpl_float_cart_v2 import MkplFloatCartV2
from test_dir.api.ec.ec_social.seller_rest.getsellerpostlist import GetSellerPostList
from test_dir.api.ec.ec_social.seller_rest.getsellerreviewlist import GetSellerReviewList

mkpl_hp_cms = {'page_type': '8', 'waterfall_page_key': 'home'}
# TODO sign 前端所有的参数排序重新拼接加盐加密，暂用tb1值
sign = '71374E82269E10095F811E2187943A4F'
seller_search_sign = "4F3FF2EE8069994A771011C43BAB88DC"


class Common(weeeTest.TestCase):

    @staticmethod
    def user_common_params(weee_store='cn', lang='en', header=None):
        """
        mkpl请求公共参数 header,zipcode,sales_org_id,date，默认取地址簿第一个地址的zipcode
        :return: 公共请求header,zipcode,sales_org_id,date
        """
        # headers = Header().login_header(email=email, password=password)
        # deepcopy 公共header
        # mkpl_common_headers = copy.deepcopy(headers)
        addr_lst = QueryUserAddressList().address_list(header)
        # 取地址簿第一个地址，headers zipcode delivery data
        # addr_id = addr_lst['object'][0]['address_id']
        addr_id = jmespath(addr_lst, 'object[0].address_id')
        # zipcode = addr_lst['object'][0]['addr_zipcode']
        zipcode = jmespath(addr_lst, 'object[0].addr_zipcode')
        header['Zipcode'] = zipcode
        header['Lang'] = lang
        header['Weee-Store'] = weee_store
        simple_res = QuerySimplePreOrder().query_simple_preorder_v1(header)
        sales_org_id = jmespath(simple_res, "object.sales_org_id")
        date = jmespath(simple_res, "object.delivery_date")
        return {"addr_header": header, "zipcode": zipcode, "sales_org_id": sales_org_id,
                "date": date}

    @staticmethod
    def user_guest_params(weee_store='cn', lang='en', headers=None):
        """
        mkpl请求公共参数 header,zipcode,sales_org_id,date，默认取地址簿第一个地址的zipcode
        :return: 公共请求header,zipcode,sales_org_id,date
        """
        # headers = Header().login_header(email=email, password=password)
        # deepcopy 公共header
        charlie_common_headers = copy.deepcopy(headers)
        addr_lst = QueryUserAddressList().address_list(charlie_common_headers)
        # 取地址簿第一个地址，headers zipcode delivery data
        # addr_id = addr_lst['object'][0]['address_id']
        addr_id = jmespath(addr_lst, 'object[0].address_id')
        # zipcode = addr_lst['object'][0]['addr_zipcode']
        zipcode = jmespath(addr_lst, 'object[0].addr_zipcode')
        charlie_common_headers['Zipcode'] = zipcode
        charlie_common_headers['Lang'] = lang
        charlie_common_headers['Weee-Store'] = weee_store
        simple_res = QuerySimplePreOrder().query_simple_preorder_v1(charlie_common_headers)
        sales_org_id = jmespath(simple_res, "object.sales_org_id")
        date = jmespath(simple_res, "object.delivery_date")
        return {"charlie_common_headers": charlie_common_headers, "zipcode": zipcode, "sales_org_id": sales_org_id,
                "date": date}

    @property
    def recommend_session(self):
        """"生成recommend_session 格式：AUTO-TEST+时间"""
        t = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        recommend_session = "AUTO-TEST-" + str(t)
        return recommend_session

    def porder_date(self, headers):
        """
        Porder 中日期date
        :param headers:
        :return:
        """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        date = porder["deal_date"]
        return date

    def fbw_ld_detail(self, header, url, dataobject_key):
        detail = self.get(headers=header, url=url, params={'dataobject_key': dataobject_key})
        return self.response


class GlobalPlusPage(weeeTest.TestCase):
    def cms_config(self, headers, zipcode, sales_org_id, lang='en'):
        """
        global+页面cms配置
        :param headers:
        :param zipcode:
        :param sales_org_id:
        :param lang:
        :return: global+ page cms
        """
        if not headers['Lang']:
            headers['Lang'] = lang

        global_plus_page_cms = QueryPageData().query_page_data(headers=headers,
                                                               page_key=mkpl_hp_cms['waterfall_page_key'],
                                                               page_type=mkpl_hp_cms['page_type'], lang=lang,
                                                               sales_org_id=sales_org_id,
                                                               zipcode=zipcode)
        return global_plus_page_cms

    def page_component_keys(self, global_plus_page_cms):
        """
         获取所有cms的component key list
        :param global_plus_page_cms:
        :return: cms component object
        """
        components = jmespath(global_plus_page_cms, "object.layout.sections[0].components")
        component_key_lst = []
        for component in components:
            component_key_lst.append(component['component_key'])
        return component_key_lst

    def cms_key_component(self, global_plus_page_cms, component_key):
        """
         获取对应key的cms的component
        :param component_key: cms 组件key
        :param global_plus_page_cms:
        :return: cms component object
        """
        components = jmespath(global_plus_page_cms, "object.layout.sections[0].components")
        for cms_component in components:
            if cms_component['component_key'] == component_key:
                return cms_component

    def cms_component_detail(self, headers, cms_component):
        """
        global+ waterfall 组件详情 传入 首页cms的 component 返回对应组件详情（搜索框返回tips）
        cm_search_bar,cm_banner_array,cm_coupon_list,cm_lightning_deals
        :param headers:
        :param cms_component: 首页cms 组件 配置
        :return:返回组件详情，搜索框返回tips
        """
        if cms_component['component_key'] == 'cm_search_bar':
            detail = cms_component['properties']['tips']
        elif cms_component['component_key'] == 'cm_banner_array':
            datasource = cms_component['datasource'][0]
            param = {'dataobject_key': datasource}
            detail = Globalpromos().global_promos_data(headers=headers, data=param)
        elif cms_component['component_key'] == 'cm_coupon_list':
            datasource = cms_component['datasource'][0]
            detail = GetGlobalWelcomeCoupons().get_global_welcome_coupons_cms(headers=headers,
                                                                              source_key='global_waterfall',
                                                                              dataobject_key=datasource)
        elif cms_component['component_key'] == 'cm_lightning_deals':
            datasource = cms_component['datasource'][0]
            detail = GlobalLightning().global_lightning(headers=headers, dataobject_key=datasource)
        else:
            raise ValueError(
                "component_key:>>>[ {} ]is not  global+ page config ".format(cms_component['component_key']))
        return detail

    @staticmethod
    def waterfall_feed_v2(headers, recommend_session, cms_component, page_num=1,
                          from_page='global',
                          key: Optional[str] = None):

        if cms_component['component_key'] != 'cm_content_feed_v2':
            raise ValueError(
                "component_key:>>>[ {} ]is not  global+ feed config ".format(cms_component['component_key']))
        else:
            datasource = cms_component['datasource'][0]
        param = {'dataobject_key': datasource,
                 'recommend_session': recommend_session,
                 'page_num': page_num,
                 'from_page': from_page}
        if key:
            param['key'] = key
        waterfall_res = GlobalFeed().global_feed_v2(headers=headers, data=param)
        return waterfall_res

    @staticmethod
    def get_waterfall_category_keys(waterfall_res):
        """
        waterfall category
        :param waterfall_res:
        :return:
        """

        category_keys = jmespath(waterfall_res, 'object.tabs[*].key')
        return category_keys

    @staticmethod
    def get_waterfall_card_filter(waterfall_res, card_type):
        """
        解析waterfall response中的卡片类型过滤：type：banners/item/seller/normal_content_video
        :param waterfall_res: waterfall category/ loadmore 请求response
        :return: 本次请求返回对应类型卡片
        """
        if card_type in ['banners', 'item', 'seller', 'normal_content_video']:
            contents = jmespath(waterfall_res, 'object.contents')
            result = [c for c in contents if c['type'] == card_type]
            return result

        else:
            raise ValueError("card type:>>>[ {} ]is not  global+ feed config ".format(card_type))


class AllStorePage(weeeTest.TestCase):
    @staticmethod
    def store_tabs(headers):
        if not headers['Lang']:
            headers['Lang'] = 'en'
        res = Globaloriginlist().global_origin_list(headers=headers)
        tabs = jmespath(res, 'object[*]')
        return tabs

    @staticmethod
    def tab_cms_config(headers, tab, sales_org_id, zipcode):
        if not headers['Lang']:
            headers['Lang'] = 'en'
        if not headers['Weee-Store']:
            headers['Weee-Store'] = 'cn'

        tab_cms_config = QueryPageData().query_page_data(headers=headers, page_key=tab,
                                                         page_type=mkpl_hp_cms['page_type'], lang=headers['Lang'],
                                                         sales_org_id=sales_org_id,
                                                         zipcode=zipcode,
                                                         store_key=headers['Weee-Store'])
        return tab_cms_config

    @staticmethod
    def page_component_keys(tab_cms_config):
        """
         获取所有cms的component key list
        :param tab_cms_config:
        :return: cms component object
        """
        components = jmespath(tab_cms_config, "object.layout.sections[0].components")
        return components

    @staticmethod
    def cms_component_detail(headers, tab_key, cms_component):
        """
        global+ waterfall 组件详情 传入 首页cms的 component 返回对应组件详情（搜索框返回tips）
        cm_search_bar,cm_banner_array,cm_coupon_list,cm_lightning_deals
        :param tab_key:
        :param headers:
        :param cms_component: 首页cms 组件 配置
        :return:返回组件详情，搜索框返回tips
        """
        if not headers['Lang']:
            headers['Lang'] = 'en'

        if cms_component['component_key'] == 'cm_mkpl_seller_line':
            datasource = cms_component['datasource'][0]
            param = {'source_key': 'cm_mkpl_seller_line_sale', 'dataobject_key': datasource}
            detail = QueryTrendingListByDsKey().global_trending_list_v2(headers=headers, params=param)
        elif cms_component['component_key'] == 'cm_mkpl_seller_list':
            datasource = cms_component['datasource'][0]
            detail = GlobalStore().global_store_all_v2(headers=headers,
                                                       origin_ids=tab_key,
                                                       dataobject_key=datasource)
        else:
            raise ValueError(
                "component_key:>>>[ {} ]is not  global+ all store page config ".format(cms_component['component_key']))
        return detail


class SellerPage(weeeTest.TestCase):

    @staticmethod
    def seller_info(headers, seller_id):
        """
        seller page  top info
        /ec/marketplace/vendor/seller/homepage/seller_id
        :param seller_id:
        :return:seller headers info
        """
        seller_info = VendorHomePage().vendor_page_header(headers=headers, vendor_id=seller_id)
        return seller_info

    def groupbuy_check(self, seller_id):
        # todo 拼单需求P2完成后补充
        pass

    @staticmethod
    def review_tab_post_list(headers, seller_id):
        """
        seller page >review tab> post
        :param headers:common header
        :param seller_id: seller_id
        :return: seller reviews
        """
        seller_post = GetSellerPostList().get_seller_post_list(headers, seller_id)
        return seller_post

    @staticmethod
    def review_tab_review_list(headers, seller_id, start_id=0, limit=10):
        """
        seller page >review tab> reviews
        :param headers: common header
        :param seller_id: mkpl seller id
        :param start_id: review begin number
        :param limit: review request number
        :return: review of current seller
        """
        seller_reviews = GetSellerReviewList().seller_page_review(headers, seller_id, start_id=start_id, limit=limit)
        return seller_reviews

    @staticmethod
    def all_product_tab(headers, seller_id, date, sign=sign, offset: int = 0, limit: int = 20, filters=None,
                        sort: str = None):
        all_products = SearchVender().vender_all_tab_products(headers=headers, vender_id=seller_id, date=date,
                                                              sign=sign, filters=filters, sort=sort)
        return all_products

    @staticmethod
    def get_vendor_product_sorts(all_products):
        """
        seller all product返回的排序选项
        :param all_products:
        :return:  sort key list
        """
        sort_lst = [i["sort_key"] for i in jmespath(all_products, 'object.sorts')]
        return sort_lst

    @staticmethod
    def get_vendor_product_catalogue(all_products):
        """
        seller all product返回的分类选项
        :param all_products:
        :return:catalogue list
        """

        catalogue_lst = [i["catalogue_num"] for i in jmespath(all_products, 'object.categories')]
        return catalogue_lst

    @staticmethod
    def get_vendor_product_filters(all_products):
        """
        all product response中filter key
        :param all_products:
        :return: filter key list
        """
        filter_lst = [i["property_key"] for i in jmespath(all_products, 'object.filters')]
        # 去除key=5的产地filter
        filter_lst.remove('5')
        return filter_lst

    @staticmethod
    def get_vendor_product_made_in_filter(all_products):
        """
        all product response中产地 key
        :param all_products:
        :return: 产地 key list
        """
        for i in jmespath(all_products, 'object.filters'):
            if i["property_key"] == '5':
                made_in_lst = jmespath(i, "property_values[*].value_key")
        return made_in_lst

    def explore_cms_config(self, headers, seller_id, zipcode, sales_org_id, lang='en'):
        """
        global+页面cms配置
        :param seller_id:
        :param headers:
        :param zipcode:
        :param sales_org_id:
        :param lang:
        :return: seller page cms
        """
        if not headers['Lang']:
            headers['Lang'] = lang

        seller_page_cms = QueryPageData().query_page_data(headers=headers, page_key=seller_id,
                                                          page_type=str(6), lang=lang,
                                                          sales_org_id=sales_org_id,
                                                          zipcode=zipcode)
        return seller_page_cms

    def cms_component_detail(self):
        pass


class SellerDetail(weeeTest.TestCase):
    def store_detail(self, headers, seller_id):
        pass

    def summary(self, headers, seller_id):
        pass

    def feedback(self, headers, seller_id, start_id):
        pass


class SellerSearch(weeeTest.TestCase):
    def search_keyword(self, headers, seller_id, date, keyword, sign):
        lang = headers["lang"]
        zipcode = headers["zipcode"]
        result = MkplSearch().seller_search_keyword(self, headers, seller_id, date, lang, zipcode, keyword, sign)
        return result


class GlobalPlusSearch(weeeTest.TestCase):
    def search_keyword(self, headers, seller_id, date, keyword, sign):
        lang = headers["lang"]
        zipcode = headers["zipcode"]
        result = MkplSearch().mkpl_global_search_keyword(self, headers, seller_id, date, lang, zipcode, keyword, sign)
        return result


class FloatCart(weeeTest.TestCase):
    @staticmethod
    def float_cart(headers):
        carts = MkplFloatCartV2().mkpl_float_cart_v2(headers=headers)
        return carts

    @staticmethod
    def seller_cart(headers, seller_id):
        seller_cart = MkplFloatCartV2().mkpl_seller_cart_v2(headers=headers, seller_id=seller_id)
        return seller_cart

# if __name__ == '__main__':
# weeeConfig.base_url = 'https://api.tb1.sayweee.net'
# common = user_common_params(email="<EMAIL>", password="12345qwert")
# headers, date, zipcode, sales_org_id = common['headers'], common['date'], common['zipcode'], common['sales_org_id']
# global_cms_config = global_plus_page_cms_config(headers=headers, zipcode=zipcode, sales_org_id=sales_org_id,
#                                                 lang='en')
# print(global_cms_config)
# cm_search_bar = global_plus_page_cms_key_component(global_cms_config, 'cm_search_bar')
# print('cm_search_bar>>>>>>>>>', cm_search_bar)
#
# cm_banner_array = global_plus_page_cms_key_component(global_cms_config, 'cm_banner_array')
# print('cm_banner_array>>>>>>>>>', cm_banner_array)
#
# cm_coupon_list = global_plus_page_cms_key_component(global_cms_config, 'cm_coupon_list')
# print('cm_coupon_list>>>>>>>>>', cm_coupon_list)
#
# cm_lightning_deals = global_plus_page_cms_key_component(global_cms_config, 'cm_lightning_deals')
# print('cm_lightning_deals>>>>>>>>>', cm_lightning_deals)
# banner_array_detail = global_plus_page_cms_component_detail(headers, cm_banner_array)
# print('banner_array_detail>>>>>>>>>', banner_array_detail)
