# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest



class TestUpdateAccountLanguage(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_update_account_language(self, ec_login_header):
        """更新账户语言"""
        LanguageRest().update_account_language(lang="en", headers=ec_login_header)
        assert self.response["result"] is True

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)




