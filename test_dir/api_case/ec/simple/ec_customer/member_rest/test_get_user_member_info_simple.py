# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/31
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.member_rest.member_rest import GetUserMemberInfo



class TestUpdateSessionInfo(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_update_session_info(self, ec_login_header):
        """更新session信息"""
        GetUserMemberInfo().get_user_member_info(uid='7790952', headers=ec_login_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
