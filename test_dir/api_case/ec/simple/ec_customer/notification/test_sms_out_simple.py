# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/4
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.notification.central_notification import CentralNotification


class TestSmsOut(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_sms_out(self, ec_jiufen_header):
        """短信发送"""
        CentralNotification().sms_out(send_phone='5353363234', send_type='subscribe', send_key='order_confirm_track',
                                      headers=ec_jiufen_header)
        case_result = self.response
        assert self.response["result"] is True
