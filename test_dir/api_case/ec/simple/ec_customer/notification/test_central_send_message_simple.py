# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/4
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.notification.central_notification import CentralNotification



class TestCentralSendMessage(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_central_send_message(self, ec_jiufen_header):
        """central 平台发送消息通知"""
        CentralNotification().central_send_message(headers=ec_jiufen_header)
        case_result = self.response
        assert self.response["result"] is True
