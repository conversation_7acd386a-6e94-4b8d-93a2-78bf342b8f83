# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/4
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.notification.central_notification import CentralNotification


class TestQueryEmailPush(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_query_email_push(self,ec_jiufen_header):
        """查询邮件推送"""
        CentralNotification().query_email_push(send_list=['<EMAIL>'],send_type='email',send_key='verification_code',headers=ec_jiufen_header)
        case_result = list(self.response["object"]["recipient_list"])
        assert case_result is not  None
        assert self.response["result"] is True
