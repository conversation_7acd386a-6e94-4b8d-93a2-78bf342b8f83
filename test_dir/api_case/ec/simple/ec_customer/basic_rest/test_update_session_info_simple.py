# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/31
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_customer.basic_rest.basic_rest import BasicRest



class TestUpdateSessionInfo(weeeTest.TestCase):
    @weeeTest.mark.list('Smoke', 'Social', 'dev')
    def test_update_session_info(self, ec_jiufen_header):
        """更新session信息"""
        BasicRest().update_session_info(zipcode='98011', device_id='B_950051', request_uri="", headers=ec_jiufen_header)
        assert self.response.get("result"), f"response={self.response}"

