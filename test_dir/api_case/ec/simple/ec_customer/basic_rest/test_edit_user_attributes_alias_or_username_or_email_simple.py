# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/31
@Software       :  PyCharm
------------------------------------
"""
import pytest
import weeeTest

from test_dir.api.ec.ec_customer.basic_rest.basic_rest import BasicRest



class TestEditUserAttributesAliasUsernameEmail(weeeTest.TestCase):

    @pytest.mark.parametrize("type_, value", [('alias', 'alias')])
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_edit_user_attributes_alias_or_username_or_email(self, type_, value, ec_jiufen_header):
        """编辑用户的用户昵称、用户名或用户邮箱"""
        BasicRest().edit_user_attributes_alias_or_username_or_email(type=type_, value=value, headers=ec_jiufen_header)
        case_result = self.response
        assert self.response['result'] is True

