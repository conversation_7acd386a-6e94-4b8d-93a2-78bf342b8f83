# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_live_activity import AccountLiveActivity



class TestUpdateLiveActivity(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_update_live_activity(self, ec_jiufen_header):
        """更新 live activity 内容"""
        AccountLiveActivity().update_live_activity(headers=ec_jiufen_header)
        assert self.response['result'] is True

