# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""
import pytest
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_setting import AccountSetting



class TestUpdateAccountPersonalSetting(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    @pytest.mark.parametrize("switch_change, userId, expected_result", [('Y', '7790952', True)])
    def test_update_account_personal_setting(self, switch_change: str, userId: str, expected_result, ec_jiufen_header):
        """更新个人用户信息设置"""
        AccountSetting().update_account_personal_setting(switch_change=switch_change, userId=userId, headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        if expected_result:
            assert case_result is not None
        else:
            assert case_result is None
        assert self.response['result'] is True
