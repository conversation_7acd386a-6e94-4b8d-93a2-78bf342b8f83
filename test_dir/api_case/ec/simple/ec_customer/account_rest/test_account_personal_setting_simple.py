# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_customer.account_rest.account_setting import AccountSetting



class TestAccountPersonalSetting(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_account_personal_setting(self, ec_jiufen_header):
        """个人用户信息设置"""
        AccountSetting().account_personal_setting(headers=ec_jiufen_header)
        assert self.response['result'] is True
        assert self.response['object']['personal_set'] == 'Y'


