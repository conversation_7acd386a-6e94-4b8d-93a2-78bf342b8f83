# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_sms_setting import AccountSmsSetting



class TestAccountSmsSetting(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_account_sms_setting(self, ec_jiufen_header):
        """用户sms 信息设置"""
        AccountSmsSetting().account_sms_setting(headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None


