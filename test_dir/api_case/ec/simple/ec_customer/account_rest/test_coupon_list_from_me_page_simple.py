# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest



class TestCouponListFromMePage(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'dev')
    def non_test_coupon_list_from_me_page(self, ec_jiufen_header):
        """me页面对应的coupon信息"""
        # system error
        AccountRest().coupon_list_from_me_page(headers=ec_jiufen_header)
        assert self.response['result'] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
