# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/4
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_info_cache import AccountInfoCache



class TestAccountInfoCache(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'tb1')
    def test_account_info_cache(self, ec_jiufen_header):
        """清除用户信息"""
        AccountInfoCache().account_info_cache(headers=ec_jiufen_header)
        assert self.response['result'] is True

