# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/2
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_so import AccountSo



class TestAccountSimpleSo(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_account_simple_so(self, ec_jiufen_header):
        """用户信息"""
        AccountSo().account_simple_so(headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None

