# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest



class TestMePagePcAccountPortal(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_me_page_pc_account_portal(self, ec_jiufen_header):
        """PC对应平台me 页面的用户信息"""
        AccountRest().me_page_pc_account_portal(headers=ec_jiufen_header)
        case_result = list(self.response["object"]["sections"])
        assert case_result is not None

        assert self.response['result'] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
