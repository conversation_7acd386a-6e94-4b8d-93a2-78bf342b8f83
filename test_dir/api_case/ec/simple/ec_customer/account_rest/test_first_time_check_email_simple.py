# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/2
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_event import AccountEvent



class TestFirstTimeCheckEmail(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'Social', 'dev')
    def test_first_time_check_email(self, ec_jiufen_header):
        """第一次校验邮箱是否注册"""
        AccountEvent().first_time_check_email(value='<EMAIL>', headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
        assert self.response['result'] is True
