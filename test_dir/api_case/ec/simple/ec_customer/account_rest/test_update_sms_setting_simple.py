# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_sms_setting import AccountSmsSetting



class TestUpdateAccountSmsSetting(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_update_sms_setting(self, ec_jiufen_header):
        """更新sms 设置"""
        AccountSmsSetting().update_sms_setting(headers=ec_jiufen_header)
        assert self.response['result'] is True

