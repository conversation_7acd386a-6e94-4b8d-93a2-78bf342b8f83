# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest



class TestMePageAccountPortalVersionV4(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_me_page_account_portal_version_v4(self, ec_jiufen_header):
        """获取me页面对应的用户信息"""
        AccountRest().me_page_account_portal_version_v4(headers=ec_jiufen_header)
        case_result = list(self.response["object"]["sections"])
        assert case_result is not None
        assert self.response['result'] is True

