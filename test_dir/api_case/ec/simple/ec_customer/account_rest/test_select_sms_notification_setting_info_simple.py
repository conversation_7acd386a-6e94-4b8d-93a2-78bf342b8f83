# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest



class TestSelectSmsNotificationSettingInfo(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_select_sms_notification_setting_info(self,  ec_jiufen_header):
        """sms 通知信息设置"""
        AccountRest().select_sms_notification_setting_info(headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        if True:
            assert case_result is not None

        assert self.response['result'] is True
