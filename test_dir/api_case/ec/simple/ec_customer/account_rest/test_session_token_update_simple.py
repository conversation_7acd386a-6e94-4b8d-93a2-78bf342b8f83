# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_live_activity import AccountLiveActivity



class TestSessionTokenUpdate(weeeTest.TestCase):
    @weeeTest.mark.list('Smoke', 'Social', 'dev')
    def test_session_token_update(self, ec_jiufen_header):
        """ 更新session token 值"""
        AccountLiveActivity().session_token_update(headers=ec_jiufen_header)
        assert self.response['result'] is True
