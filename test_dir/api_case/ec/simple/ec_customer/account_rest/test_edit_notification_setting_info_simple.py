# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest



class TestEditNotificationSettingInfo(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_edit_notification_setting_info(self, ec_jiufen_header):
        """ 编辑通知设置信息"""
        AccountRest().edit_notification_setting_info(headers=ec_jiufen_header)
        assert self.response['result'] is True

