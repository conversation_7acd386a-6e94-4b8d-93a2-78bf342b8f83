# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest


class TestUpdateUserProfileInfo(weeeTest.TestCase):
    # @weeeTest.mark.parametrize()
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_update_user_profile_info(self, ec_jiufen_header):
        """更新用户profile信息"""
        AccountRest().update_user_profile_info(headers=ec_jiufen_header)
        assert self.response['result'] is True
