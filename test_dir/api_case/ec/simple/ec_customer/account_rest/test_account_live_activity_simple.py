# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_live_activity import AccountLiveActivity


class TestAccountLiveActivity(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'fail_product')
    def test_account_live_activity(self, ec_jiufen_header):
        """激活用户信息"""
        AccountLiveActivity().account_live_activity(headers=ec_jiufen_header)
        dict_data = self.response
        # assert self.response['result'] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
