# -*- coding: utf-8 -*-
"""
<AUTHOR>  sufen xu
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.user.user_bak import User


class TestUser(weeeTest.TestCase):
    @weeeTest.params.data([("<EMAIL>", "qwer1234", True), ("<EMAIL>", "A1234567", False)])
    # @weeeTest.mark.list('Regression-skip', 'Social')
    def test_user_info(self, email, password, expected_result):
        """用户信息"""
        # 以前就标注有SKIP标签
        user = User()
        user.anon_auth()
        user.email_register(email=email, password=password)
        result = self.response["result"]
        assert expected_result == result


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', title='验证新老用户')
