# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_customer.favorites.favorites import Favorites



class TestGetAccountInfoByUserId(weeeTest.TestCase):
    # 接口已不再使用
    # @weeeTest.params.data([True])
    # @weeeTest.mark.list('Regression', 'Smokeee', 'Social', 'fail_product')

    def deprecated_test_get_account_info_by_user_id(self,ec_jiufen_header):
        """根据user id获取对应的用户信息"""
        # EmailLogin().email_login(headers=headers, email='<EMAIL>', password='1')
        Favorites().get_account_info_by_user_id(headers=ec_jiufen_header)
        case_result = self.response
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)