# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_customer.favorites.favorites import Favorites



class TestFavoritesSetSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_favorite_set_simple(self, ec_login_header):
        """test_favorite_set_simple"""
        Favorites().favorites_set(headers=ec_login_header, target_id=90427)
        # 断言
        assert self.response["result"] is True


