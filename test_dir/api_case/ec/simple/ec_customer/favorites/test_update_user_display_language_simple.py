# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

import weeeTest

from test_dir.api.ec.ec_customer.favorites.favorites import Favorites



class TestUpdateUserDisplayLanguage(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'fail_product', 'dev')
    def test_update_user_display_language(self, ec_login_header):
        """更新语言设置"""
        Favorites().update_user_display_language(headers=ec_login_header,lan="vi")
        case_result = self.response
        assert self.response["result"] is True


