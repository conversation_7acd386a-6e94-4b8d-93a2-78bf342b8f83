import pymysql
import uuid

import weeeTest
from weeeTest import weeeConfig

from qa_config.basic_db import ConnectDatabase
from qa_config.ec_sql import EcDB
""" # type:discount_all,discount_partial,discount_special_price,discount_lightning """

class createpromotion(weeeTest.TestCase):
    EcDB.get_discount(type='discount_all', vendor_id=7319)
    EcDB.get_discount(type='discount_partial', vendor_id=7319)
    EcDB.get_discount(type='discount_special_price', vendor_id=7319)
    EcDB.get_discount(type='discount_lightning', vendor_id=7319)

    def create_discount(self, headers, vender_id: int, type: str, title: str, end_time: int,
                        start_time: int, factor: int, products: str = None
                        ):
        # products=[[
        #   {
        #     "id": 1961367
        #   },
        #   {
        #     "id": 1961366
        #   }
        # ]]
        data = {
            "type": type,
            "title": title,
            "start_time": start_time,
            "end_time": end_time,
            "factor": factor,
            "products": [products],
        }

        self.post(url=f'/central/mkpl/admin/vendors/{vender_id}/discounts', headers=headers, json=data)
        return self.response

    def create_special(self, headers, vender_id: int, type: str, title: str, end_time: int,
                       start_time: int, id: int, special_price: str ):
        data = {
            "type": type,
            "title": title,
            "start_time": start_time,
            "end_time": end_time,
            "products": [
                {
                    "id": id,
                    "special_price": special_price
                }
            ]
        }

        self.post(url=f'/central/mkpl/admin/vendors/{vender_id}/discounts', headers=headers, json=data)
        return self.response

    def create_lightning(self, headers, vender_id: int, type: str, title: str, end_time: int, start_time: int,
                         id: int, special_price: str,max_cart_quantity:int,quantity:int):

        data = {
            "type": type,
            "title": title,
            "start_time": start_time,
            "end_time": end_time,
            "products":  [
                {
                    "id": id,
                    "special_price": special_price,
                    "max_cart_quantity": max_cart_quantity,
                    "quantity": quantity
                }
            ],
        }

        self.post(url=f'/central/mkpl/admin/vendors/{vender_id}/discounts', headers=headers, json=data)
        return self.response

    def Search_products(self, headers, vendor_id: int):
        data = {
            "vendor_id": vendor_id,
            "business_type": "seller",
            "keyword": "",
            "startColumn": 0,
            "pageSize": 40,
            "web_status_list": [
                "A",
                "X"
            ],
            "fields": [
                "price",
                "image_url",
                "id",
                "title",
                "title_en",
                "base_price",
                "min_order_quantity"
            ],
            "sales_org_id": 30
        }

        self.post(url='/central/im/product/search', headers=headers, json=data)
        return self.response["object"]["data"]


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
