import weeeTest

from test_dir.api.ec.ec_content.config.query_by_configKey_and_lang import QueryByConfigKeyAndLang



class TestQueryByConfigKeyAndLangSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'products', 'dev')
    def test_query_by_configKey_and_lang(self, ec_login_header):
        """query_by_configKey_and_lang"""
        QueryByConfigKeyAndLang().query_by_configKey_and_lang(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True


