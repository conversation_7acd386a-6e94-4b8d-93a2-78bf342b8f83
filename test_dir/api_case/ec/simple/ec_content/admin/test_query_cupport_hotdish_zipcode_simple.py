# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.ec_content.admin.query_cupport_hotdish_zipcode import QueryCupportHotdishZipcode
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestQueryCupportHotdishZipcode(weeeTest.TestCase):
    def non_test_query_cupport_hotdish_zipcode(self, ec_login_header):
        """ # 单接口测试/ec/content/admin/get_hotdish_zipcode """
        # 经与huimin确认，此接口已废弃
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        QueryCupportHotdishZipcode().query_cupport_hotdish_zipcode(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
