# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.ec_content.admin.query_sales_org_list import QuerySalesOrgList


class TestQuerySalesOrgList(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'tb1', 'dev')
    def test_query_sales_org_list(self, ec_login_header):
        """ # 单接口测试/ec/content/sales_org/list """
        # 获取用户preorder
        QuerySalesOrgList().query_sales_org_list(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True


