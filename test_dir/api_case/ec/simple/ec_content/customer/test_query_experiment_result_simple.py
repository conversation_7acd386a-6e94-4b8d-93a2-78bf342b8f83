import weeeTest
from test_dir.api.ec.ec_content.customer.query_experiment_result import QueryExperimentResult


class TestQueryExperimentResultSimple(weeeTest.TestCase):

    
    @weeeTest.data.file(file_name='ec_content_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', "fail_product", 'dev')
    def test_query_experiment_result(self, *args, ec_login_header):
        """QueryExperimentResult"""
        # porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        experiment_id = args[0]["experiment"]["experiment_id"][0]

        QueryExperimentResult().query_experiment_result(headers=ec_login_header, experiment_id=experiment_id)
        # 断言
        assert self.response["result"] is True


