import uuid

import weeeTest

from test_dir.api.ec.ec_content.global_rest.global_feed import GlobalFeed
from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest


recommend_session = str(uuid.uuid4())


class TestGlobalFeed(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_global_feed(self, *args, ec_login_header):
        """waterfall分类及分类下商家，视频，商品"""
        LanguageRest().update_account_language(lang="en", headers=ec_login_header)
        lang = ec_login_header["Lang"]

        if lang in ("zh", "ja", "zh-Hant", "vi"):
            GlobalFeed().global_feed(headers=ec_login_header,
                                     recommend_session=recommend_session,
                                     key=args[0]["waterfall_category"]["default"][2])
        elif lang == "en":
            # store = headers["Weee-Store"] 临时修改，header key 批量upper
            store = ec_login_header["Weee-Store"]
            print(lang, store)
            if store == "ko":
                GlobalFeed().global_feed(headers=ec_login_header,
                                         recommend_session=recommend_session,
                                         key=args[0]["waterfall_category"]["ko"][2])
            elif store == "es":
                GlobalFeed().global_feed(headers=ec_login_header,
                                         recommend_session=recommend_session,
                                         key=args[0]["waterfall_category"]["es"][2])
            elif store == "in":
                GlobalFeed().global_feed(headers=ec_login_header,
                                         recommend_session=recommend_session,
                                         key=args[0]["waterfall_category"]["in"][2])
            elif store == "ph":
                GlobalFeed().global_feed(headers=ec_login_header,
                                         recommend_session=recommend_session,
                                         key=args[0]["waterfall_category"]["ph"][2])
            else:
                GlobalFeed().global_feed(headers=ec_login_header,
                                         recommend_session=recommend_session,
                                         key=args[0]["waterfall_category"]["default"][2])
        else:
            print("no lang or store")

        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
