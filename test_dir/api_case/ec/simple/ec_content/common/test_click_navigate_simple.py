# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  huimin.li
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/8/15 09:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/15 09:06
"""

import weeeTest
from test_dir.api.ec.ec_content.common.click_navigate import ClickNavigate


class TestClickNavigate(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_content_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_click_navigate(self, *args, ec_login_header):

        ClickNavigate().click_navigate(headers=ec_login_header, config_id=args[0]["preorder"]["config_id"],
                                       zipcode=args[0]["preorder"]["zipcode"])

        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
