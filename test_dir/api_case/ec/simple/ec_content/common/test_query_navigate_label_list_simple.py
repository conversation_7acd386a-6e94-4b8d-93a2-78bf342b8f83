# !/usr/bin/python3
# -*- coding: utf-8 -*-

import click

import weeeTest

from test_dir.api.ec.ec_content.common.query_navigate_label_list import QueryNavigateLabelList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestQueryNavigateLabelListSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_navigate_label_list(self, ec_login_header):
        """ # 单接口测试/ec/content/navigate/label/list: 点击底部tab """
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        zipcode = int(porder["zipcode"])
        QueryNavigateLabelList().query_navigate_label_list(headers=ec_login_header, zipcode=zipcode)
        # 断言
        assert self.response["result"] is True

