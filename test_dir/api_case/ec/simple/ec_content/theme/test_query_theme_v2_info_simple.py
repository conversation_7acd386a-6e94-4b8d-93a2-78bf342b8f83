import weeeTest

from test_dir.api.ec.ec_content.theme.query_theme_v2_info import QueryThemeV2Info
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestQueryThemeV2InfoSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product')
    def test_query_theme_v2_info(self, ec_login_header):
        """query_theme_v2_info"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        QueryThemeV2Info().query_theme_v2_info(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True



