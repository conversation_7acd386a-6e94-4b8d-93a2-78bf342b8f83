import weeeTest
from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestQueryPageData(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_page_data(self, ec_mkpl_header):
        """ global+ 页面 cms组件 """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_mkpl_header.get("addr_header"))["object"]
        zipcode = porder["zipcode"]
        QueryPageData().query_page_data(headers=ec_mkpl_header.get("addr_header"), page_key="recommend", page_type="8", lang="en", sales_org_id=1,
                                        mode=None, zipcode=zipcode)
        assert self.response["result"] is True

