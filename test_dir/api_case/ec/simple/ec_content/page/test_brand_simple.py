import weeeTest

from test_dir.api.ec.ec_content.page.brand import Brand
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestBrandSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_content_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_brand(self, *args, ec_login_header):
        """brand"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        sales_org_id = int(porder["sales_org_id"])
        brand_key = args[0]["brand"]["brand_key"][0]
        Brand().brand(ec_login_header, brand_key)
        # 断言
        assert self.response["result"] is True

