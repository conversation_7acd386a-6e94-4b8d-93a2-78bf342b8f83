import weeeTest

from test_dir.api.ec.ec_content.collection.collection_grocery_share import CollectionGroceryShare


class TestCollectionGroceryShareSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_collection_grocery_share(self, ec_login_header):
        """test_collection_grocery_share"""
        # 分享合集
        CollectionGroceryShare().collection_grocery_share(headers=ec_login_header, collection_key="tzzmz")
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
