import weeeTest

from test_dir.api.ec.ec_content.collection.get_collection_grocery_detail import GetCollectionGroceryDetail


class TestGetCollectionGroceryDetailSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_collection_grocery_detail(self, ec_login_header):
        """get_collection_grocery_detail
        """
        GetCollectionGroceryDetail().get_collection_grocery_detail(headers=ec_login_header, collection_key="515n3")
        assert self.response["result"] is True

