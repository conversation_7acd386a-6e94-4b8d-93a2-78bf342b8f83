import weeeTest
from test_dir.api.ec.ec_content.collection.deal_of_week_collection import DealOfWeekCollection


class TestDealOfWeekSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_deal_of_week_collection(self, ec_login_header):
        """ test_deal_of_week_collection """
        DealOfWeekCollection().deal_of_week_collection(headers=ec_login_header)
        assert self.response["result"] is True
        return self.response


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
