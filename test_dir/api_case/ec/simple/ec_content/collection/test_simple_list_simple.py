# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.ec_content.collection.get_collection_grocery_simple_list import GetCollectionGrocerySimpleList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestSimpleList(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_content_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_simple_list(self, *args, ec_login_header):
        """ # 单接口测试/ec/content/collection/grocery/simple/list"""
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        collection_key = args[0]["collection"]["collection_key"][0]
        # 非必填 sales_org_id = int(porder["sales_org_id"])

        GetCollectionGrocerySimpleList().get_collection_grocery_simple_list(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
        return self.response


if __name__ == '__main__':
    # weeeTest.main(base_url='https://api.tb1.sayweee.net',case_list=["D:\\MyWork\\Python\\EC-demo\\qa-script-master\\test_dir\\api_case\\ec\\simple\\ec_mkt\\banner\\test_get_banner_list_simple.py::TestGetBannerList::test_get_banner_list"])
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
