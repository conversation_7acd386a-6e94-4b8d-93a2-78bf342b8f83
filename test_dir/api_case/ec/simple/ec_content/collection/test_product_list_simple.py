# !/usr/bin/python3
# -*- coding: utf-8 -*-

import click

import weeeTest

from test_dir.api.ec.ec_content.collection.get_collection_grocery_product_list import GetCollectionGroceryProductList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestProductList(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_product_list(self, ec_login_header):
        """ # Winback合集产品列表接口"""

        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        collection_key = "tzal4"
        # 非必填 sales_org_id = int(porder["sales_org_id"])

        GetCollectionGroceryProductList().get_collection_grocery_product_list(headers=ec_login_header,
                                                                              collection_key=collection_key)
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    # weeeTest.main(base_url='https://api.tb1.sayweee.net',case_list=["D:\\MyWork\\Python\\EC-demo\\qa-script-master\\test_dir\\api_case\\ec\\simple\\ec_mkt\\banner\\test_get_banner_list_simple.py::TestGetBannerList::test_get_banner_list"])
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
