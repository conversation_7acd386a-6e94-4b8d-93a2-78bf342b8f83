# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.ec_content.collection.get_collection_grocery_detail import GetCollectionGroceryDetail
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestDetailSimple(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_content_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_detail(self, *args, ec_login_header):
        """ # 单接口测试,获取合集detail"""
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        date = porder["delivery_pickup_date"]
        lang = args[0]["collection"]["lang"][0]
        collection_key = args[0]["collection"]["collection_key"][0]

        GetCollectionGroceryDetail().get_collection_grocery_detail(headers=ec_login_header, collection_key=collection_key, lang=lang, date=date)
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    # weeeTest.main(base_url='https://api.tb1.sayweee.net',case_list=["D:\\MyWork\\Python\\EC-demo\\qa-script-master\\test_dir\\api_case\\ec\\simple\\ec_mkt\\banner\\test_get_banner_list_simple.py::TestGetBannerList::test_get_banner_list"])
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
