import weeeTest

from test_dir.api.ec.ec_mkt.banner.banner_main_banner_list import BannerMainBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestBannermainbannerlistSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.skip('Global的Banner业务已下线')
    def non_test_main_banner_list(self, *args, ec_login_header):
        """ # 获取Global的Banner信息测试-业务已下线 """

        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        BannerMainBannerList().main_banner_list(headers=ec_login_header, dataobject_key=args[0]["dataobject_key_global"][
            "ds_main_banner_recommend"], date=porder["delivery_pickup_date"])
        assert self.response["result"] is True
        return self.response


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
