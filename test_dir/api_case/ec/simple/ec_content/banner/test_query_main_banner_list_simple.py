# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from test_dir.api.ec.ec_mkt.banner.query_main_banner_list import QueryMainBannerList


class TestQueryMainBannerListSimple(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_content_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_main_banner_list(self, *args, ec_login_header):
        """ # 单接口测试/ec/content/banner/main_banner_list """
        # dataobject_key = ["ds_main_banner","ds_notice_banner"]
        dataobject_key = args[0]["banner"]["dataobject_key"][0]
        # "dataobject_key": ["ds_main_banner","ds_notice_banner"]
        QueryMainBannerList().query_main_banner_list(headers=ec_login_header, dataobject_key=dataobject_key)
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
