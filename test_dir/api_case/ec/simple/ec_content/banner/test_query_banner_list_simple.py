import weeeTest

from test_dir.api.ec.ec_mkt.banner.query_banner_list import QueryBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestQueryBannerListSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_content_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_banner_list(self, *args, ec_login_header):
        """query_banner_list"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        sales_org_id = int(porder["sales_org_id"])
        lang = "zh"
        dataobject_key = args[0]["banner"]["dataobject_key"][0]
        type = args[0]["banner"]["type"][0]
        QueryBannerList().query_banner_list(headers=ec_login_header, dataobject_key=dataobject_key,
                                            lang=lang, type=type, sales_org_id=sales_org_id)
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
