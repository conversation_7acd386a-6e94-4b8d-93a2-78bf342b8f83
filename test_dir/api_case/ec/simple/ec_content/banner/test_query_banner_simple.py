# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_mkt.banner.query_banner import QueryBanner
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestQueryBannerSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_banner(self, ec_login_header):
        """ # 单接口测试/ec/content/banner: 获取首页轮播图 """
        # type = ["carousel", "portal_top","portal_banner"]
        # dataobject_key = ["ds_main_banner","ds_notice_banner"]
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        date = porder["delivery_pickup_date"]
        deal_id = porder["deal_id"]
        sales_org_id = int(porder["sales_org_id"])
        zipcode = int(porder["zipcode"])

        # type = ["carousel", "portal_top"]
        QueryBanner().query_banner(ec_login_header, "carousel", sales_org_id)
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
