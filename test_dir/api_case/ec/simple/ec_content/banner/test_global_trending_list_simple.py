import weeeTest
from test_dir.api.ec.ec_mkt.banner.global_trending_list import QueryTrendingListByDsKey


class TestqueryTrendingListByDsKeySimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_global_trending_list(self, *args, ec_login_header):
        """# 获取Global热销商家的信息"""
        QueryTrendingListByDsKey().global_trending_list(headers=ec_login_header,
                                                        source_key=args[0]["dataobject_key_global"]["source_key"],
                                                        dataobject_key=args[0]["dataobject_key_global"][
                                                            "dataobject_key_treding"])

        assert self.response["result"] is True
        product_id = self.response["object"][0]["products"][0]["id"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
