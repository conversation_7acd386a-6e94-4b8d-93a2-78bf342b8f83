import weeeTest
from test_dir.api.ec.ec_item.product.pdp_static_detail import PdpStaticDetail


class TestPdpStaticDetailSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_pdp_static_detail(self, ec_login_header):
        """test_pdp_static_detail"""
        PdpStaticDetail().pdp_static_detail(headers=ec_login_header, product_id=10395)
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None
