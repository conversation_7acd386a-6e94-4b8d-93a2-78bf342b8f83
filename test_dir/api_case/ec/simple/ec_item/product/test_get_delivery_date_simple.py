import weeeTest

from test_dir.api.ec.ec_item.product.get_delivery_date import GetDeliveryDate



class TestGetDeliveryDateSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'tb1', 'dev')
    def test_get_delivery_date(self, ec_login_header):
        """get_delivery_date"""
        # 此用例不迁往线上
        GetDeliveryDate().get_delivery_date(headers=ec_login_header, product_id=2802)
        # 断言
        assert self.response["result"] is True


