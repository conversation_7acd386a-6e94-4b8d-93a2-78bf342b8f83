import weeeTest

from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail



class TestPdpDetail(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_pdp_detail(self, ec_login_header):
        """test_pdp_detail"""
        # product_id最好不要使用固定值
        PdpDetail().pdp_detail(headers=ec_login_header, product_id=10395)
        # 断言
        assert self.response["result"] is True
