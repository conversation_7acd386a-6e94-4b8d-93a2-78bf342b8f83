import weeeTest

from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_target import DsCollectionTarget



class TestDsCollectionTargetSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_ds_collection_target(self, ec_login_header):
        """test_ds_collection_target"""
        DsCollectionTarget().ds_collection_target(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
