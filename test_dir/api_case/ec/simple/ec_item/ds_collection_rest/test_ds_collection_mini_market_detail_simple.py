import weeeTest
from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_mini_market_detail import DsCollectionMiniMarketDetail



class TestDsCollectionMiniMarketDetailSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'fail_product', 'dev')
    def test_ds_collection_mini_market_detail(self, ec_login_header):
        """# mini Market 查询"""
        DsCollectionMiniMarketDetail().ds_collection_mini_market_detail(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
