import weeeTest

from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_home import DsCollectionHome



class TestDsCollectionHomeSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'dev')
    def test_ds_collection_home(self, ec_login_header):
        """ # 合集商品查询--首页 """
        DsCollectionHome().ds_collection_home(headers=ec_login_header, ds_url="cm_item_exposure_collection",
                                              dataobject_key="ds_item_list_exposurecollection_486245")
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
