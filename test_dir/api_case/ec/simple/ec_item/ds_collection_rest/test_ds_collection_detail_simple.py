import weeeTest
from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_detail import DsCollectionDetail



class TestDsCollectionDetailSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'dev')
    def test_ds_collection_detail(self, ec_login_header):
        """# 合集商品查询--详情页"""

        DsCollectionDetail().ds_collection_detail(headers=ec_login_header, ds_url="cm_item_exposure_collection",
                                                  dataobject_key="ds_item_list_exposurecollection_486245")
        # 断言
        assert self.response["result"] is True


