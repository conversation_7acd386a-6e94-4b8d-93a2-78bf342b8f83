# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_item.cuisine.api_cuisine import ApiCuisine


class TestSelectCuisine(weeeTest.TestCase):

    @weeeTest.mark.skip("这个接口有问题")
    def test_select_cuisine(self, ec_login_header):
        """# 菜系选择(调研页调用,skip也需要调用)"""
        ApiCuisine().select_cuisine(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
