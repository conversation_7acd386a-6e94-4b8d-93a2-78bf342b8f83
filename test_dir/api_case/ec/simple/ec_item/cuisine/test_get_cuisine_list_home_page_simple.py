# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_item.cuisine.api_cuisine import ApiCuisine



class TestGetCuisineListHomePage(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke','Transaction', 'dev')
    def test_get_cuisine_list_home_page(self, ec_login_header):
        """ # 菜系查询(首页) """
        ApiCuisine().get_cuisine_list_home_page(headers=ec_login_header)
        if self.response["object"]["total_count"] > 0:
            print("存在cuisine数据")
        else:
            print("不存在cuisine数据")
        # 断言
        assert self.response["result"] is True

