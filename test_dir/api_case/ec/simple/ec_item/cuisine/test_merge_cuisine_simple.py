# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_item.cuisine.api_cuisine import ApiCuisine


class TestMergeCuisine(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_merge_cuisine(self, ec_login_header):
        """# 菜系merge(内部服务-登录时调用)"""
        ApiCuisine().merge_cuisine(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
