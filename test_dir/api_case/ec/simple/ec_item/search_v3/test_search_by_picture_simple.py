# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_picture import SearchByPicture


class TestSearchByPictureSimple(weeeTest.TestCase):

    @weeeTest.mark.skip("接口废弃，404")
    def test_search_by_picture_v1(self, ec_login_header):
        """test_search_by_picture_v1"""
        SearchByPicture().search_by_picture_v1(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True


