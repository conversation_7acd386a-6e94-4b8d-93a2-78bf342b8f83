# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_item.search_v3.v3_search import V3Search
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestV3SearchSimple(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_v3_search_simple(self, *args, ec_login_header):
        """test_v3_search_simple"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        V3Search().v3_search(headers=ec_login_header, filter_key_word="fruit", date=date, zipcode=zipcode)
        V3Search().v3_search_history(headers=ec_login_header, filter_key_word="fruit", date=date, zipcode=zipcode)
        V3Search().v3_search_category(headers=ec_login_header, filter_key_word="fruit",
                                      key=args[0]["waterfall_category"]["default"][2], date=date, zipcode=zipcode)

        assert self.response["result"] is True

