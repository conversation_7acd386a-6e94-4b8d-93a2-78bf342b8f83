# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_post import SearchPost


class TestSearchPostSimple(weeeTest.TestCase):

    @weeeTest.mark.skip("接口有问题，待确认")
    def test_search_post_v1(self, ec_login_header):
        """test_search_post_v1"""
        SearchPost().search_post_v1(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_post_v2(self, ec_login_header):
        """test_search_post_v2"""
        SearchPost().search_post_v2(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
