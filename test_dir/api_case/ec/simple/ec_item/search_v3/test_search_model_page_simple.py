# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest


from test_dir.api.ec.ec_item.search_v3.search_model_page import SearchModelPage


class TestSearchModelPageSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip("接口有问题，待确认")
    def test_search_model_page(self, *args, ec_login_header):
        """test_search_model_page"""
        SearchModelPage().search_model_page(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

