# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_item.search_v3.search_by_custom import SearchByCustom


class TestSearchByCustomSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip("非页面调用接口,平台服务，别的业务会调用")
    def test_search_by_custom(self, ec_login_header):
        """非页面调用接口,平台服务，别的业务会调用"""
        SearchByCustom().search_by_custom(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

