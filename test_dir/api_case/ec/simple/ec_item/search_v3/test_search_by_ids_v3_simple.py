# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from test_dir.api.ec.ec_item.search_v3.search_by_ids_v3 import SearchByIdsV3


class TestSearchByIdsV3Simple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'dev','test_search_by_ids_v3')
    def test_search_by_ids_v3(self, ec_login_header):
        """test_search_by_ids_v3"""
        ids = 106988,107193,106476
        SearchByIdsV3().search_by_ids_v3(headers=ec_login_header, source="ds_product_1724881963906",
                                         ids=ids)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'fail_product','test_search_by_ids_cms_v3')
    def test_search_by_ids_cms_v3(self, ec_login_header):
        """test_search_by_ids_cms_v3"""
        # 这个接口有问题，始终报 system error
        ids = 106988,107193,106476
        SearchByIdsV3().search_by_ids_cms_v3(headers=ec_login_header,source="ds_product_1724881963906",
                                             ids=ids)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product')
    def test_search_by_ids_mini_v3(self, ec_login_header):
        """test_search_by_ids_mini_v3"""
        SearchByIdsV3().search_by_ids_mini_v3(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
