# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue import SearchByCatalogue



class TestSearchByCatalogueSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_by_catalogue(self, *args, ec_login_header):
        """特殊分类搜索：test_search_by_catalogue"""

        data = args[0]["category"]["search_by_catalogue"]
        for item in data:
            SearchByCatalogue().search_by_catalogue(headers=ec_login_header, filter_sub_category=item["filter_sub_category"],
                                                    dataobject_key=item["dataobject_key"])
        # 断言
        assert self.response["result"] is True
