# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from test_dir.api.ec.ec_item.search_v3.search_by_keyword_v3 import SearchByKeywordV3


class TestSearchByKeywordV3Simple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_by_keyword_v3(self, *args, ec_login_header):
        """test_search_by_keyword_v3"""
        SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header, filter_key_word=args[0]["search"]["filter_key_word"])
        # 断言
        assert self.response["result"] is True

