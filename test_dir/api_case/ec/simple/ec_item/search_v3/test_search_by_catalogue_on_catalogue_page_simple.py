# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_on_catalogue_page import SearchByCatalogueOnCataloguePage


class TestSearchByCatalogueOnCataloguePageSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_by_catalogue_on_catalogue_page(self, ec_login_header):
        """普通分类：search_by_catalogue_on_catalogue_page"""
        SearchByCatalogueOnCataloguePage().search_by_catalogue_on_catalogue_page(headers=ec_login_header, special_num="sale",
                                                                                 catalogue_num="green06")
        # 断言
        assert self.response["result"] is True
