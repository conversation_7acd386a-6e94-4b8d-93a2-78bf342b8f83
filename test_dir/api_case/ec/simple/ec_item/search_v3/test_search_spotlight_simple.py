# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_spotlight import SearchSpotlight


class TestSearchSpotlightSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_spotlight(self, ec_login_header):
        """test_search_spotlight"""
        SearchSpotlight().search_spotlight(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
