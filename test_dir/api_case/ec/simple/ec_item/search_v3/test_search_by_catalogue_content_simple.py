# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent


class TestSearchByCatalogueContentSimple(weeeTest.TestCase):

    
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_by_catalogue_content(self, ec_login_header):
        """普通分类搜索：test_search_by_catalogue_content"""
        SearchByCatalogueContent().search_by_catalogue_content(headers=ec_login_header, filter_sub_category="green")
        assert self.response["result"] is True
