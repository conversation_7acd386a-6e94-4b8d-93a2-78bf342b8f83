# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_item.theme.api_themes import SearchThemes


class TestSearchThemesSimple(weeeTest.TestCase):

    
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip("接口有问题，待确认")
    def test_search_themes(self, ec_login_header):
        """test_search_themes"""
        SearchThemes().search_themes(headers=ec_login_header, tag_id=1)
        # 断言
        assert self.response["result"] is True

