# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.common.query_navigate_label_list import QueryNavigateLabelList



class TestQueryNavigateLabelList(weeeTest.TestCase):

    @weeeTest.mark.skip("这个用例没写完")
    def test_query_navigate_label_list(self, ec_login_header):
        """test_query_navigate_label_list"""
        QueryNavigateLabelList().query_navigate_label_list(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

