# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from test_dir.api.ec.ec_item.suggest.search_hot_keywords import SearchHotKeywords


class TestSearchHotKeywords(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_hot_keywords(self, ec_login_header):
        """test_search_hot_keywords"""

        SearchHotKeywords().search_hot_keywords(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
