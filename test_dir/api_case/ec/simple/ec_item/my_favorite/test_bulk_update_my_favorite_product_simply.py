import weeeTest

from test_dir.api.ec.ec_item.my_favorite.api_favorites import ApiFavorites



class TestBulkUpdateMyFavoriteProduct(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_bulk_update_my_favorite_product(self, *args, ec_login_header):
        """test_bulk_update_my_favorite_product"""
        ApiFavorites().favorites_batch_batch(headers=ec_login_header, product_ids=args[0]["product_ids"])
        # 断言
        assert self.response["result"] is True
