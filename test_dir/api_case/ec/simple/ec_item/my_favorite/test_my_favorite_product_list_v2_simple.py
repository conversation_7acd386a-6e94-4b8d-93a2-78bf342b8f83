import weeeTest

from test_dir.api.ec.ec_item.my_favorite.api_favorites import ApiFavorites
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder



class TestMyFavoriteProductListV2Simple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_my_favorite_product_list_v2(self, ec_login_header):
        """my_favorite_product_list_v2"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        ApiFavorites().my_favorite_product_list_v2(headers=ec_login_header, zipcode=porder["zipcode"])
        # 断言
        assert self.response["result"] is True
