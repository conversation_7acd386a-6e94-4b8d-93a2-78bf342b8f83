import weeeTest

from test_dir.api.ec.ec_item.promotion_search.search_by_ps_id import SearchByPsId



class TestSearchByPsIdSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction')
    def test_search_by_ps_id(self, ec_login_header):
        """test_search_by_ps_id"""
        # ps_id为固定值，不迁往线上
        SearchByPsId().search_by_ps_id(headers=ec_login_header, ps_id=7054)
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None

