import weeeTest
from test_dir.api.ec.ec_item.recommend.get_new_for_you_products import GetNewForYouProducts



class TestGetNewForYouProductsSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_new_for_you_products(self, ec_login_header):
        """get_new_for_you_products"""
        GetNewForYouProducts().get_new_for_you_products(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

