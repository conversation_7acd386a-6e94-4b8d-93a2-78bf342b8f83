# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.record.search_submit_noresult import SearchSubmitNoresult



class TestSearchSubmitNoResult(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_submit_no_result(self, *args, ec_login_header):
        """提交空搜索想要的产品"""
        SearchSubmitNoresult().search_submit_noresult(headers=ec_login_header,
                                                      noresult_keyword=args[0]["search"]["noresult_keyword"])
        # 断言
        assert self.response["result"] is True

