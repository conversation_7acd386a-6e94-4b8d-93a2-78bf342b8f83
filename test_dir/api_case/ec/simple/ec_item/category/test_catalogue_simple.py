# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
from test_dir.api.ec.ec_item.category.catalogue import Catalogue


class TestCatalogue(weeeTest.TestCase):

    @weeeTest.mark.skip("这个用例没写完")
    def test_catalogue(self, *args, ec_login_header):
        """test_catalogue"""
        Catalogue().catalogue(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_catalogue_filter_normal(self, ec_login_header):
        """获取普通分类列表"""
        ApiCatalogues().catalogue_filter_normal(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

