import weeeTest
from test_dir.api.ec.ec_item.product_v2.get_products_related import GetProductsRelated



class TestGetProductsRelatedSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_products_related(self, ec_login_header):
        """test_get_products_related"""
        GetProductsRelated().get_products_related(headers=ec_login_header, product_id=10395)
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None

