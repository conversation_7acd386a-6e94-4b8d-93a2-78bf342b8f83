import weeeTest

from test_dir.api.ec.ec_item.product_v2.get_buy_together_products import GetBuyTogetherProducts



class TestGetBuyTogetherProductsSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_buy_together_products(self, ec_login_header):
        """test_get_buy_together_products"""
        GetBuyTogetherProducts().get_buy_together_products(headers=ec_login_header, product_id=10395)
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None

