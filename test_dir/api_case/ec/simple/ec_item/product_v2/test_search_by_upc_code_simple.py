import weeeTest

from test_dir.api.ec.ec_item.product_v2.search_by_upc_code import SearchByUpcCode



class TestSearchByUpcCodeSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_by_upc_code(self, ec_login_header):
        """test_search_by_upc_code"""
        SearchByUpcCode().search_by_upc_code(headers=ec_login_header, upc_code="69500049045542")
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None

