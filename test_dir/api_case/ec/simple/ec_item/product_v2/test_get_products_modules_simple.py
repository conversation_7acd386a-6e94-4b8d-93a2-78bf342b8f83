import weeeTest

from test_dir.api.ec.ec_item.product_v2.get_products_modules import GetProductsModules
from test_dir.api.ec.ec_item.product_v2.get_products_modules_second import GetProductsModulesSecond



class TestGetProductsModulesSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_products_modules(self, ec_login_header):
        """test_get_products_modules"""
        GetProductsModules().get_products_modules(headers=ec_login_header, product_id=10395)
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None

    @weeeTest.mark.list('Transaction', 'product')
    def test_get_products_modules_second(self, ec_login_header):
        """me页面Recently Viewed模块"""
        GetProductsModulesSecond().get_products_modules_second(headers=ec_login_header, product_id=10395, from_page="home")
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None

