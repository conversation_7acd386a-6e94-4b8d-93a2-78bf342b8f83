# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from test_dir.api.ec.ec_item.cuisine.api_cuisine import ApiCuisine
from test_dir.api.ec.ec_item.search_v2.search_by_cuisine import SearchByCuisine



class TestSearchByCuisineSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'fail_product')
    # 这个接口有问题，报system error
    def test_search_by_cuisine(self, ec_login_header):
        """test_search_by_cuisine"""
        SearchByCuisine().search_by_cuisine(headers=ec_login_header, filterCuisine="chinese")
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_by_cuisine_home_page(self, ec_login_header):
        """test_search_by_cuisine_home_page"""
        SearchByCuisine().search_by_cuisine_home_page(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_search_by_cuisine_filter(self, ec_login_header):
        """test_search_by_cuisine_filter"""
        # 获取有哪些cuisine
        ApiCuisine().get_cuisine_list_home_page(headers=ec_login_header)
        if self.response["object"]["total_count"] > 0:
            print("存在cuisine数据")
            filterCuisine = self.response["object"]["cuisine_list"]
            my_list = []
            for value in filterCuisine:
                print(value["cuisine_key"])
                my_list.append(value["cuisine_key"])
                SearchByCuisine().search_by_cuisine_filter(headers=ec_login_header, filterCuisine=value["cuisine_key"])
                # 断言
                assert self.response["result"] is True
            print(my_list)
        else:
            print("不存在cuisine数据")
            assert self.response["result"] is True

