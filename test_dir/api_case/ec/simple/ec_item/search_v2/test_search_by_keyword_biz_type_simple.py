# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.search_v2.search_by_keyword_biz_type import SearchByKeywordBizType



class TestSearchByKeywordBizTypeSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'fail_product')
    def test_search_by_keyword_biz_type(self, ec_login_header):
        """test_search_by_keyword_biz_type"""
        # 这个接口有问题，始终报400
        SearchByKeywordBizType().search_by_keyword_biz_type(headers=ec_login_header)
        # 断言
        # assert self.response["result"] is True

