# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import click

import weeeTest

from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestGetBannerListSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_banner_list(self, ec_login_header):
        """ # 单接口测试/ec/ec_mkt/banner: 获取首页轮播图 """
        # type = ["carousel", "portal_top","portal_banner"]
        # dataobject_key = ["ds_main_banner","ds_notice_banner"]
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        date = porder["delivery_pickup_date"]
        # deal_id = porder["deal_id"]
        sales_org_id = int(porder["sales_org_id"])
        zipcode = int(porder["zipcode"])
        lang = "en"
        # type = ["carousel", "portal_top"]
        GetBannerList().get_banner_list(headers=ec_login_header, type="carousel", dataobject_key="ds_main_banner",
                                        sales_org_id=sales_org_id, lang=lang, date=date)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_banner_list_portal_top(self, ec_zhuli_header):
        """ # 单接口测试/ec/ec_mkt/banner: PC获取首页广告位 """
        # type = ["carousel", "portal_top"]
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)["object"]
        date = porder["delivery_pickup_date"]
        deal_id = porder["deal_id"]
        sales_org_id = int(porder["sales_org_id"])
        zipcode = int(porder["zipcode"])
        lang = "en"

        GetBannerList().get_banner_list(headers=ec_zhuli_header, type="portal_top", dataobject_key="ds_notice_banner",
                                        sales_org_id=sales_org_id, lang=lang, date=date)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_banner_list_portal_banner(self, ec_zhuli_header):
        """ # 单接口测试/ec/ec_mkt/banner: PC获取首页广告位 """
        # type = ["carousel", "portal_top"]

        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)["object"]
        date = porder["delivery_pickup_date"]
        deal_id = porder["deal_id"]
        sales_org_id = int(porder["sales_org_id"])
        zipcode = int(porder["zipcode"])
        lang = "en"

        GetBannerList().get_banner_list(headers=ec_zhuli_header, type="portal_banner", dataobject_key="ds_notice_banner",
                                        sales_org_id=sales_org_id, lang=lang, date=date, is_rtg=1)
        # 断言
        assert self.response["result"] is True

