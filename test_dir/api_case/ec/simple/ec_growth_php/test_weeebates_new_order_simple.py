# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  yue.wang
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/28
# @Software       :  PyCharm
# ------------------------------------
# """
# import weeeTest
# from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin
# from test_data.ec.simple.common import LoginHeader
# from test_dir.api.ec.ec_growth_php.weeebates import Weeebates
#
# class TestWeeebatesNewOrder(weeeTest.TestCase):
#     @weeeTest.params.data([(37707373,True)])
#     def test_weeebates_new_order(self,orderId:str,expected_result):
#         headers = LoginHeader().login_header(email='<EMAIL>', password='12')
#         EmailLogin().email_login(headers=headers, email='<EMAIL>', password='12')
#         Weeebates().weeebates_new_order(headers=headers,orderId=orderId)
#         case = self.response
#         case_result = case["object"]
#         if expected_result:
#             assert case_result is not None
#         else:
#             assert case_result is None
#         return case_result
#
# if __name__ == '__main__':
#     weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)