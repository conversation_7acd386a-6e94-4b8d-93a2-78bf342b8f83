# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo



class TestPraiseReview(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1')
    def test_praise_review(self, ec_jiufen_header):
        """review获得的收益"""
        ReviewInfo().praise_review(reviewId='1372842',status='C',headers=ec_jiufen_header)
        assert self.response['result'] is True

