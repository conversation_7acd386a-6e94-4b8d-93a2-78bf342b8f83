# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo



class TestQueryReviewLikedUserList(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1')
    def test_query_review_liked_user_list(self, ec_jiufen_header):
        """查询用户对应点赞的列表"""
        ReviewInfo().query_review_liked_user_list(reviewId='1591359',headers=ec_jiufen_header)
        dict_data =self.response


