# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_product_info.post_product_info import PostProductInfo



class TestPostProductV1(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_post_product(self, ec_login_header):
        """产品晒单信息"""
        PostProductInfo().post_product_V1(filter_key_word='rice', zipcode='94538', trigger_type='search_popular',
                                          headers=ec_login_header)
        assert self.response['result'] is True
