"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.insight_rest.insight_rest import InsightRest



class TestGetAccountInsights(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_get_account_insights(self, ec_jiufen_header):
        """获取对应账户的收入信息"""
        # User().email_login(self,email='<EMAIL>',password='1')
        InsightRest().get_account_insights(headers=ec_jiufen_header)
        dict_data = self.response
        assert self.response['result'] is True



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net',env='tb1',debug=True)
