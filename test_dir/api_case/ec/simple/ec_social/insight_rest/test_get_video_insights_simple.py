"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.insight_rest.insight_rest import InsightRest


class TestGetAccountInsights(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_get_video_insights(self, ec_jiufen_header):
        """获取视频的收益信息"""
        InsightRest().get_video_insights(post_id='57567', headers=ec_jiufen_header)
        dict_data = self.response
        assert self.response['result'] is True
