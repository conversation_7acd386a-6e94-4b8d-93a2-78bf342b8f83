# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/18
# @Software       :  PyCharm
# ------------------------------------
# """

import weeeTest

from test_dir.api.ec.ec_social.event.event import Event


class TestEventRemindByUser(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1', 'dev')
    def test_event_remind_by_user(self,ec_jiufen_header):
        """提醒用户对应的event事件"""
        Event().event_remind_by_user(event_id=55,status='A',headers=ec_jiufen_header)
        case_result = self.response
        assert case_result is not None

