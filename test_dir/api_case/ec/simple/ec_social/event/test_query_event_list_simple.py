# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/18
# @Software       :  PyCharm
# ------------------------------------
# """

import weeeTest
from test_dir.api.ec.ec_social.event.event import Event


class TestQueryEventList(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_query_event_list(self, ec_jiufen_header):
        """查询event列表"""
        Event().query_event_list(headers=ec_jiufen_header)
        case_result = list(self.response["object"])
        assert case_result is not None
