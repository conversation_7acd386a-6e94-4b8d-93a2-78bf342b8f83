# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  ji<PERSON><PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/17
# @Software       :  PyCharm
# ------------------------------------
# """

import weeeTest

from test_dir.api.ec.ec_social.event.event import Event


class TestEventListShare(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_event_list_share(self, ec_jiufen_header):
        """分享event清单列表"""
        Event().event_list_share(headers=ec_jiufen_header)
        case_result = list(self.response["object"]["share_content"])
        assert case_result is not None


    @weeeTest.mark.list('tb1')
    def test_single_event_share(self,ec_jiufen_header):
        Event().single_event_share(id='1',headers=ec_jiufen_header)
        assert self.response['result'] is True

