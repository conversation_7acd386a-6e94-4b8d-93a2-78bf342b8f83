# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/18
# @Software       :  PyCharm
# ------------------------------------
# """

import weeeTest
from test_dir.api.ec.ec_social.event.event import Event


class TestQuerySuggeestionEvent(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'tb1', 'dev')
    def test_query_suggestion_event(self,ec_jiufen_header):
        """查询suggestion event列表"""
        Event().query_suggestion_event(filter_id='2',headers=ec_jiufen_header)
        case_result =self.response
        assert case_result is not None
