# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/18
# @Software       :  PyCharm
# ------------------------------------


import weeeTest

from test_dir.api.ec.ec_social.event.event import Event


class TestEventDetail(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'tb1', 'dev')
    def test_event_detail(self, ec_jiufen_header):
        """获取对应的event 对应的详情信息"""
        Event().event_detail(id='5',headers=ec_jiufen_header)
        case_result = list(self.response["object"])
        assert case_result is not None


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net',env='tb1',debug=True)

