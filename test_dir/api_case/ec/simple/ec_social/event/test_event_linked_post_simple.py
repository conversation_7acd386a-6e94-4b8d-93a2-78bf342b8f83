# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/18
# @Software       :  PyCharm
# ------------------------------------
# """

import weeeTest

from test_dir.api.ec.ec_social.event.event import Event


class TestEventLinkedPost(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_event_linked_post(self,ec_jiufen_header):
        """event 晒单信息"""
        Event().event_linked_post('2',headers=ec_jiufen_header)
        case_result = list(self.response["object"]["list"])

        assert case_result is not None
