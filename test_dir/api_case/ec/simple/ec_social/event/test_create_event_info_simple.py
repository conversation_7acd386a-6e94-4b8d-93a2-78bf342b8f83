# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.event.event import Event


class TestCreateEventInfo(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_create_event_info(self, ec_jiufen_header):
        """创建event信息"""
        Event().create_event_info(headers=ec_jiufen_header)
        case_result = list(self.response["object"])
        # object返回对应的数据信息
        assert case_result is not None



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)