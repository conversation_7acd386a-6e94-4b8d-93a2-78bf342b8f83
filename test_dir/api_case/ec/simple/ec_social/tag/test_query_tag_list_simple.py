# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.tag.tag import Tag



class TestQueryTagList(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'Social', 'dev')
    def test_query_tag_list(self, ec_jiufen_header):
        """查询hashtag列表"""
        Tag().query_tag_list(headers=ec_jiufen_header)
        case_result = list(self.response["object"])
        assert case_result is not None
