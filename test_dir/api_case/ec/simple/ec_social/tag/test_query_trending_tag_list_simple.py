# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_social.tag.tag import Tag



class TestQueryTrendingTagList(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'Social', 'dev')
    def test_query_trending_tag_list(self, ec_jiufen_header):
        """查询hashtag 对应的trending 的 列表"""
        Tag().query_trending_tag_list(headers=ec_jiufen_header)
        case_result = list(self.response["object"])
        assert case_result is not None

