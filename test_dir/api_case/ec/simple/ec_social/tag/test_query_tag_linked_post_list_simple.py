# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.tag.tag import Tag



class TestQueryTagLinkedPostList(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1')
    def test_query_tag_linked_post_list(self,ec_jiufen_header):
        """查询hashtag 对应链接的晒单列表"""
        Tag().query_tag_linked_post_list(id='55',headers=ec_jiufen_header)
        case_result = list(self.response["object"]["list"])
        assert case_result is not None
