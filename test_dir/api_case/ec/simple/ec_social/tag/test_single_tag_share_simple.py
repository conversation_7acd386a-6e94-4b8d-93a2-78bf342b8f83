# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_social.tag.tag import Tag



class TestSingleTagShare(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1')
    def test_single_tag_share(self,ec_jiufen_header):
        """单个hashtag 分享"""
        Tag().single_tag_share(id='55',headers=ec_jiufen_header)
        case_result = list(self.response["object"]["share_content"])
        assert case_result is not None
