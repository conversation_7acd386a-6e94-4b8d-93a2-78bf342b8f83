# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_social.tag.tag import Tag



class TestQueryTrendingTagListWithFilter(weeeTest.TestCase):
    @weeeTest.mark.list('Social')
    def test_query_trending_tag_list_with_filter(self, ec_jiufen_header):
        """查询hashtag 对应的trending 的filter"""
        Tag().query_trending_tag_list_with_filter(headers=ec_jiufen_header)
        case_result = list(self.response["object"]["list"])
        assert case_result is not None
