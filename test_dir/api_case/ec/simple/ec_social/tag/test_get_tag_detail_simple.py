# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_social.tag.tag import Tag



class TestGetTagDetail(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1')
    def test_get_tag_detail(self, ec_jiufen_header):
        """获取hashtag 对应的详情"""
        Tag().get_tag_detail(id='55', headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
