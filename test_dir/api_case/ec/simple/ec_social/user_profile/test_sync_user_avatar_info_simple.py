# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestSyncUserAvatarInfo(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_sync_user_avatar_info(self, ec_jiufen_header):
        """系统用户的等级"""
        UserProfile().sync_user_avatar_info(headers=ec_jiufen_header)
        case_result = self.response
        assert case_result is not None
