# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestGetUserSelectionPrivilegeForProduct(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_get_user_selection_privilege_for_product(self,ec_jiufen_header):
        """获取用户对应的权限"""
        UserProfile().get_user_selection_privilege_for_product(headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
