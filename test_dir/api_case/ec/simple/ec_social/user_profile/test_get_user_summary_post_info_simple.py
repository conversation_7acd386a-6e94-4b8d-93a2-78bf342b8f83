# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestGetUserSummaryPostInfo(weeeTest.TestCase):
    @weeeTest.params.data([('7790952',True)])
    @weeeTest.mark.list('Social', 'tb1')
    def test_get_user_summary_post_info(self, ec_jiufen_header):
        """获取用户晒单信息"""
        UserProfile().get_user_summary_post_info(headers=ec_jiufen_header,uid='7790952')
        dict_data = self.response
        case_result =dict_data["object"]
        assert case_result is not None



