# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestQueryUserPostList(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_query_user_post_list(self, ec_jiufen_header):
        """查看视频的晒单列表"""
        UserProfile().query_user_post_list(type='video', headers=ec_jiufen_header)
        case_result = self.response["object"]["list"]
        assert case_result is not None
