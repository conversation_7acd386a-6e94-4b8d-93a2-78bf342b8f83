# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestDeleteUserRecommendedUser(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_delete_user_recommended_user(self,ec_jiufen_header):
        """删除推荐用户"""
        UserProfile().delete_user_recommended_user(id='1203149086',headers=ec_jiufen_header)
        assert self.response['result'] is True

