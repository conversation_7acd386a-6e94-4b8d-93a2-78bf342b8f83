# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile

class TestShareUserInfo(weeeTest.TestCase):

    @weeeTest.params.data([('7790952',True)])
    @weeeTest.mark.list('Social', 'tb1')
    def test_share_user_info(self, ec_jiufen_header):
        """分享用户信息"""
        UserProfile().share_user_info(userId='7790952', headers=ec_jiufen_header)
        dict_data = self.response
        case_result =dict_data["object"]
        assert case_result is not  None
