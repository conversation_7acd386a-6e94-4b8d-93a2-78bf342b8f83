# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestQueryUserFollowingList(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_query_user_following_list(self, ec_jiufen_header):
        """查看用户的关注清单"""
        UserProfile().query_user_following_list(headers=ec_jiufen_header,uid='7790952')
        dict_data =self.response
        case_result =dict_data["object"]
        assert case_result is not None




