# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestQeryUserLikedPostList(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_query_user_liked_post_list(self, ec_jiufen_header):
        """查看用户晒单的点赞清单"""
        UserProfile().query_user_liked_post_list(headers=ec_jiufen_header)
        dict_data = self.response
        case_result =dict_data["object"]
        assert case_result is not None
