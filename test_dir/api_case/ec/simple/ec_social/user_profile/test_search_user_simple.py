# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_profile.user_profile import UserProfile



class TestSearchUser(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'fail_product')
    def test_search_user(self, ec_jiufen_header):
        """ 搜索用户"""
        UserProfile().search_user(headers=ec_jiufen_header)
        assert self.response['result'] is True
