# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_social.search.search_for_keyword import SearchForKeyword



class TestSearchForKeyword(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'Social', 'dev')
    def test_search_for_keyword(self, ec_jiufen_header):
        """用户在社区关键字进行搜索"""
        SearchForKeyword().search_for_keyword(headers=ec_jiufen_header, keyword='花')
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
