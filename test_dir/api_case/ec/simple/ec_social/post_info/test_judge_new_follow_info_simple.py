# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestJudgeNewFollowInfo(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_judge_new_follow_info(self, ec_jiufen_header):
        """判断新关注的信息"""
        PostInfo().judge_new_follow_info(headers=ec_jiufen_header)
        dict_data = self.response
        assert self.response['result'] is True

