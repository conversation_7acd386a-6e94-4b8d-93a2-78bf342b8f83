# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestQueryUserPostList(weeeTest.TestCase):
    @weeeTest.params.data([('7790952',True)])
    @weeeTest.mark.list('Social', 'tb1')
    def test_query_user_post_list(self, ec_jiufen_header):
        """查询用户的晒单列表"""
        PostInfo().query_user_post_list(userId='7790952',headers=ec_jiufen_header)
        case_result =self.response
        assert case_result is not None
