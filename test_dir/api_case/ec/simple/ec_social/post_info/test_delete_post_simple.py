# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin

class TestDeletePost(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_delete_post(self, ec_jiufen_header):
        """"删除晒单"""
        # 固定id，不往线上迁
        EmailLogin().email_login(headers=ec_jiufen_header, email='<EMAIL>', password='wo466125')
        PostInfo().delete_post(id='44875',headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
