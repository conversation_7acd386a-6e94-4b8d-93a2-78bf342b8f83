# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestPintoTop(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_pinto_top(self, ec_jiufen_header):
        """测试视频置顶"""
        # 固定id不往线上迁
        PostInfo().pinto_top(postId='3023',headers=ec_jiufen_header)
        case_result = self.response
