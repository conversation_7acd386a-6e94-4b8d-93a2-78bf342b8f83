# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestQueryPostCommentList(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1')
    def test_query_post_comment_list(self, ec_jiufen_header):
        """查询晒单评论列表"""
        # 固定ID不迁往线上
        PostInfo().query_post_comment_list(postId='21453',headers=ec_jiufen_header)
        case_result = list(self.response["object"]["list"])
        assert case_result is not None

