# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo

class TestPraisePostComment(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_praise_post_comment(self, ec_login_header):
        """获取评论点赞"""
        # 固定ID不迁往线上
        # EmailLogin().email_login(headers=headers, email='<EMAIL>', password='1')
        PostInfo().praise_post_comment(postId='45652', commentId='130927', headers=ec_login_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is None

