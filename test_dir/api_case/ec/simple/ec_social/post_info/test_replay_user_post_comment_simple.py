import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
from test_data.ec.simple.common import Header

class TestReplayUserPostComment(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_replay_user_post_comment(self, ec_jiufen_header):
        """用户晒单评论的回复列表"""
        PostInfo().replay_user_post_comment(postId='17219',commentId='19582',headers=ec_jiufen_header)
        assert self.response['result'] is True
