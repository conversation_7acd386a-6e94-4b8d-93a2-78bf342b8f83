# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestUserOwnPost(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_user_own_post(self, ec_jiufen_header):
        """获取用户自己的晒单信息"""
        PostInfo().user_own_post(headers=ec_jiufen_header)
        case_result = self.response["object"]
        assert case_result is not None
