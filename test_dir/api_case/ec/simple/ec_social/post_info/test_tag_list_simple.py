# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestTagList(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'product', 'dev')
    def test_tag_list(self, ec_jiufen_header):
        """hashtag 列表"""
        PostInfo().tag_list(headers=ec_jiufen_header)
        case_result = list(self.response['object'])
        assert case_result is not None
