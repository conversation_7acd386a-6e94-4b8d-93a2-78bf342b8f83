# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestPostShare(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_post_share(self, ec_jiufen_header):
        """晒单分享"""
        # 固定ID不迁往线上
        PostInfo().post_share(postId='3023',headers=ec_jiufen_header)
        dict_data=self.response
        case_result =dict_data["object"]
        assert case_result is not None
