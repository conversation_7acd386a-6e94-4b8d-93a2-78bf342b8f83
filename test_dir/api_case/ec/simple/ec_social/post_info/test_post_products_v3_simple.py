# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestPostProductsV3(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_post_products_v3(self,ec_jiufen_header):
        """对应产品的晒单列表"""
        # 固定ID不迁往线上
        PostInfo().post_products_v3(id='3923',headers=ec_jiufen_header)
        dict_data=self.response
        case_result =dict_data["object"]
        assert case_result is not None
