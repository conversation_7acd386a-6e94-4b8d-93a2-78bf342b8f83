"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestUpdateVideoTranslate(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'fail_product')
    def test_update_video_translate(self, ec_jiufen_header):
        """更新视频翻译"""
        PostInfo().update_video_translate(headers=ec_jiufen_header)
        assert self.response['result'] is True

