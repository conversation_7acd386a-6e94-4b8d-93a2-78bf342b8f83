# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestQueryUserFollowingPostListV1(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_query_user_following_post_list_v1(self, ec_jiufen_header):
        """查询用户关注对应的晒单列表"""
        PostInfo().query_user_following_post_list_v1(headers=ec_jiufen_header)
        case_result =list(self.response["object"])
        assert case_result is not None
