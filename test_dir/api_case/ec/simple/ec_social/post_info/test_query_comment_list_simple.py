import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo

class TestQueryPostCommentList(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_query_post_comment_list(self, ec_jiufen_header):
        """查询评论列表"""
        # 固定ID不迁往线上
        PostInfo().query_post_comment_list(postId='3023',headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
