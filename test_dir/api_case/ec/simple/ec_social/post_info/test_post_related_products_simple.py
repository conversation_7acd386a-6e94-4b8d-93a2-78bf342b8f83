# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo



class TestPostRelatedProducts(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1')
    def test_post_related_products(self, ec_jiufen_header):
        """晒单相关产品信息"""
        # 固定ID不迁往线上
        PostInfo().post_related_products(product_ids='7937',headers=ec_jiufen_header)
        case_result = list(self.response["object"])
        assert case_result is not None
