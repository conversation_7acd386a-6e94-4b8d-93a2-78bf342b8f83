# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo

class TestPraisePost(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_praise_post(self, ec_jiufen_header):
        """晒单点赞"""
        # 固定ID不迁往线上
        PostInfo().praise_post(postId='21453',headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is None
