import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo

class TestNewPostComment(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_new_post_comment(self, ec_jiufen_header):
        """新晒单评论"""
        # 固定postId不往线上迁
        # EmailLogin().email_login(headers=headers, email='<EMAIL>', password='1')
        PostInfo().new_post_comment(postId='17219',content='测试',headers=ec_jiufen_header)
        case_result = self.response["object"]
        assert case_result is not None
