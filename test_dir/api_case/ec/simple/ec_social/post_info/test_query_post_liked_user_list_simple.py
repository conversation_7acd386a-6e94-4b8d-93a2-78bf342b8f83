# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo

class TestQueryPostLikedUserList(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_query_post_liked_user_list(self, ec_jiufen_header):
        """查询晒单对应的用户点赞列表"""
        # 固定ID不迁往线上
        PostInfo().query_post_liked_user_list(postId='3023',headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
