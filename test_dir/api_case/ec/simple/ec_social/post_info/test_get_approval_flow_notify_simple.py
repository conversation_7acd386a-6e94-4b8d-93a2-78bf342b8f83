# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_social.post_info.post_info import PostInfo


class TestGetApprovalFlowNotify(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_get_approval_flow_notify(self, ec_jiufen_header):
        """"关注用户"""
        PostInfo().get_approval_flow_notify(headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
