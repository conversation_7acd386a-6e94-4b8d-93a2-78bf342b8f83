# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo

class TestGetPostShare(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_get_post_share(self, ec_jiufen_header):
        """分享晒单"""
        # 固定id，不迁往线上
        PostInfo().get_post_share(post_id='1591359',headers=ec_jiufen_header)
        case_result = self.response
