# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api.ec.ec_social.message.message import Message


class TestMessageCenter(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_message_read_notice(self, ec_jiufen_header):
        """查看消息,红点消息"""
        Message().message_read_notice(headers=ec_jiufen_header)
        case_result = self.response
        assert case_result is not None
