# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/18
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_social.message.message import Message


class TestMessageSummary(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_message_summary(self, ec_jiufen_header):
        """查看汇总消息"""
        Message().message_summary(headers=ec_jiufen_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
