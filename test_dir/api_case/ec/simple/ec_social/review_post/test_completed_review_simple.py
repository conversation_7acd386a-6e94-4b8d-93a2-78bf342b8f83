# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/06
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost


class TestCompletedReview(weeeTest.TestCase):

    @weeeTest.mark.list('bbb', 'Social')
    def test_completed_review(self, ec_login_header):
        """晒单已完成all页面"""
        ReviewPost().completed_review(review='review', all='all', my_review='my_review', headers=ec_login_header)
        dict_data = self.response






if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
