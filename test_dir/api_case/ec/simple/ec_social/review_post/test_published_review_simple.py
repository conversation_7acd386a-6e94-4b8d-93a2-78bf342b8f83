# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost




class TestPublishedReview(weeeTest.TestCase):


    def test_published_review(self, ec_login_header):
        """晒单已审核通过页面"""
        ReviewPost().published_review(review='review', P='P', my_review='my_review', headers=ec_login_header)
        case_result = self.response
        return case_result


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)