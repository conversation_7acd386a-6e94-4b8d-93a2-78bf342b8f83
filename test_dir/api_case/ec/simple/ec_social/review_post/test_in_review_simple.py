# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/06
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost



class TestInReview(weeeTest.TestCase):

    @weeeTest.mark.list('aaa', 'Social')
    def test_in_review(self, ec_login_header):
        """晒单已完成待审核页面"""
        ReviewPost().in_review(review='review', A='A', my_review='my_review', headers=ec_login_header)
        dict_data = self.response
