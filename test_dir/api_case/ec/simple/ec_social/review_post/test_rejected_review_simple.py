# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost




class TestRejectedReview(weeeTest.TestCase):


    def test_rejected_review(self, ec_login_header):
        """晒单审核不通过页面"""
        ReviewPost().rejected_review(review='review', R='R', my_review='my_review', headers=ec_login_header)
        case_result = self.response
