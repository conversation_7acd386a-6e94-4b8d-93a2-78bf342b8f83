# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/06
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost



class TestReviewList(weeeTest.TestCase):



    def test_to_review_list(self, ec_login_header):
        ReviewPost().to_review_list(headers=ec_login_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
