# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost



class TestReviewUpload(weeeTest.TestCase):

    @weeeTest.mark.list('Social')
    def test_review_upload(self, ec_login_header):
        """发布晒单添加图片"""
        case_result = ReviewPost().review_upload(image='image', social='social', headers=ec_login_header)
        print("===>", case_result)

        assert (case_result["object"][0]["url"]).startswith("https")
