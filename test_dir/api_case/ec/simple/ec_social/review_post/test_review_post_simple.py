# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost


class TestReviewUpload(weeeTest.TestCase):


    def test_review_post(self, ec_login_header):
        """发布晒单"""
        ReviewPost().review_post(order_id='order_id', product_id='product_id', headers=ec_login_header)
        case_result = self.response
        print("===>", case_result)
        return case_result



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
