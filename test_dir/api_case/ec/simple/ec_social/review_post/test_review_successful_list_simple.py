# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.review_post.review_post import ReviewPost



class TestReviewSuccessList(weeeTest.TestCase):


    def test_review_successful_list(self, ec_login_header):
        """发布晒单成功"""
        ReviewPost().review_successful_list( headers=ec_login_header)
        case_result = self.response["object"]["list"]
        print("case_result===>", case_result)

        for item in case_result:
            ReviewPost().review_post(item["order_id"], item["product_id"], headers=ec_login_header)
            assert self.response["message_id"] == "10000"




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
