# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest

from test_dir.api.ec.ec_social.track.track import Track


class TestTrackVideoShareInfo(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_track_video_share_info(self, ec_jiufen_header):
        """视频对应的track 的分享信息"""
        Track().track_video_share_info(headers=ec_jiufen_header)
        case_result = self.response

