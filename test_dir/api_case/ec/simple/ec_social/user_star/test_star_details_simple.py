# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_star.user_star import UserStar


class TestStarDetails(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Social', 'tb1')
    def test_star_details(self, ec_login_header):
        """用户等级详细信息"""
        UserStar().star_details(headers=ec_login_header)
        dict_data = self.response
        case_result =dict_data["object"]
        assert case_result is not None
