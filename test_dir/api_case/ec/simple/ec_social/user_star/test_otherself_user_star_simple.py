# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_star.user_star import UserStar




class TestOtherselfUserStar(weeeTest.TestCase):

    @weeeTest.mark.list('Social', 'tb1')
    def test_otherself_user_star(self, ec_login_header):
        """其他用户的等级"""
        UserStar().otherself_profile_star(headers=ec_login_header,uid='7642436')
        assert self.response['result'] is True
