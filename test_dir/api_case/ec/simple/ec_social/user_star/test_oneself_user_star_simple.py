# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_social.user_star.user_star import UserStar



class TestOneselfUserStar(weeeTest.TestCase):

    @weeeTest.params.data([True])
    @weeeTest.mark.list('Social', 'tb1')
    def test_oneself_user_star(self, ec_login_header):
        """用户自己的等级信息"""
        UserStar().oneself_profile_star(headers=ec_login_header)
        dict_data = self.response
        case_result = dict_data["object"]
        assert case_result is not None
