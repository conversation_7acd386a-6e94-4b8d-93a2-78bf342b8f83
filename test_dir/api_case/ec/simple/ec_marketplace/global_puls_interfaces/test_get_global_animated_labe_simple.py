# -*- coding:utf-8 -*-
import weeeTest
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.get_global_animated_label import GetGlobalAnimatedLabel


class TestGetGlobalAnimatedLabelSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_global_animated_label(self, *args, ec_login_header):
        """# get_global_animated_label"""
        # headers_ggal = Header().login_header(email=args[0]["login"]["email"], password=args[0]["login"]["password"])
        GetGlobalAnimatedLabel().get_global_animated_label(headers=ec_login_header, zipcode=args[0]["zipcode"]["CA"])
        assert self.response["result"] is True
