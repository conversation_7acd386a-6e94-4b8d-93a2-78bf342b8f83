import weeeTest
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_introduction import GlobalIntroduction
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestGlobalIntroductionSimple(weeeTest.TestCase):

    # 获取Global的介绍信息
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_global_introduction(self, ec_mkpl_header):
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))
        zipcode = common['zipcode']

        """ test_global_introduction """
        GlobalIntroduction().global_introduction(headers=ec_mkpl_header.get("addr_header"))
        assert self.response["result"] is True
        # 确保结果中的object的introduction不为空
        assert  self.response['object']['introduction'], "Introduction in response object is empty"

        # 如果introduction不为空，则打印结果
        print(self.response['object']['introduction'])


