# from test_data.ec.simple.common import Header
import weeeTest

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import \
    ListAllAvailableSellerIds


class TestListAllAvailableSellerIds(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_list_all_available_seller_ids(self, ec_login_header):
        """# 获取waterfall的商家列表 test_list_all_available_seller_ids """
        ListAllAvailableSellerIds().list_all_available_seller_ids(headers=ec_login_header, zipcode='98011')
        assert self.response["result"] is True

