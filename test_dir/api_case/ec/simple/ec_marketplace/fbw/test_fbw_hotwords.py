import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_keyword_v3 import SearchByKeywordV3
from test_dir.api.ec.ec_item.suggest.search_hot_keywords import SearchHotKeywords



class TestFbwDirectSearch(weeeTest.TestCase):
    @weeeTest.mark.list('Regression',  'Transaction')
    def test_fbw_hot_word(self, ec_login_header):
        """
        支持fbw区域，热词包含fbw
        """
        # 98011 热词
        result = SearchHotKeywords().search_hot_keywords(headers=ec_login_header)
        # 搜索结果返回热词
        hot_keywords = result['object']['keywords']
        # 断言hot_keywords包含Fresh bakery
        assert 'Fresh bakery' in hot_keywords, 'hot_keywords:{}'.format(hot_keywords)

    @weeeTest.mark.list('Regression',  'Transaction')
    def test_fbw_hot_word_search(self, ec_login_header):
        """
        支持fbw区域，热词直搜
        """
        # 98011 搜索 Fresh bakery
        search_result = SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header, filter_key_word='Fresh bakery')
        # 搜索结果返回direct_page_url
        direct_page_url = search_result['object']['direct_page_url']
        # 断言direct_page_url包含fbw landing
        assert 'mkpl/bakery/landing' in direct_page_url, 'direct_page_url:{}'.format(direct_page_url)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
