# -*- coding:utf-8 -*-
import weeeTest
from test_dir.api.ec.ec_marketplace.mkpl_top_x.mkpl_top_x import MkplTopX


class TestMkplTopSellingSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'dev')
    def test_mkpl_top_selling_landing(self, *args, ec_login_header):
        """# 获取落地页top selling数据"""

        top_selling_landing = MkplTopX().mkpl_top_ranking(headers=ec_login_header, tab=args[0]["topX"]["topselling"],
                                                          key=args[0]["waterfall_category"]["default"][1], page_num=1)

        assert self.response["result"] is True
        # 新增断言：确保top_selling_landing["object"][0]["contents"]不为空
        assert top_selling_landing["object"][0][
            "contents"], "top_selling_landing['object'][0]['contents'] should not be empty"

