# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/19 09:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/19 09:06
"""
import weeeTest

from test_dir.api.ec.ec_marketplace.sellerinfo.seller_introduction import VendorIntroduction


class TestVendorIntroductionSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_seller_introduction(self, *args, ec_charlie_header):
        """
        #test_seller_introduction
        """

        VendorIntroduction().vendor_introduction(headers=ec_charlie_header, vendor_id=args[0]["seller_info"]["vendor_id"])
        # 断言
        assert self.response["result"] is True
