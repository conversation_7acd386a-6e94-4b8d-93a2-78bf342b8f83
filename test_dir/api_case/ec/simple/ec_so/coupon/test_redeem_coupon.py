"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_redeem_coupon.py
@Description    :  
@CreateTime     :  2023/7/22 19:47
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 19:47
"""
import weeeTest
from test_dir.api.ec.ec_so.coupon.redeem_coupon import RedeemCoupon


class TestApplyCoupon(weeeTest.TestCase):
    """对优惠券进行应用--兑换有问题，需要提供可以兑换的券码"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip('非combine checkout，兑换优惠券，后续统一调整到V2')
    def test_coupons_apply(self, *args, ec_signup_header):
        """非combine checkout，兑换优惠券，后续统一调整到V2"""
        RedeemCoupon().coupons_redeem(headers=ec_signup_header, cart_domain=args[0]["cart"]["cart_domain"],
                                      coupon_code=args[0]["preorder"]["coupon_code"])
        # 断言
        assert self.response["result"] is True

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip('有问题，待调整')
    # @weeeTest.mark.skip('需要提供可以兑换的券码')
    def test_coupons_apply_v2(self, *args, ec_signup_header):
        """Combine checkout，兑换优惠券"""
        RedeemCoupon().coupons_redeem_v2(headers=ec_signup_header, coupon_code=args[0]["preorder"]["coupon_code"])
        # 断言
        assert self.response["result"] is True

