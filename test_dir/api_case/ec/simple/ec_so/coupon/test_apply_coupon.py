"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_apply_coupon.py
@Description    :  
@CreateTime     :  2023/7/21 18:59
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/21 18:59
"""
import weeeTest

from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.coupon.apply_coupon import ApplyCoupon
from test_dir.api.ec.ec_so.coupon.redeem_coupon import RedeemCoupon
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class TestApplyCoupon(weeeTest.TestCase):
    """对优惠券进行应用"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.skip('非combine checkout，后续统一调整到V2')
    def test_coupons_apply(self, *args, ec_signup_header):
        """非combine checkout，后续统一调整到V2"""

        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_signup_header)["object"]
        # 加购商品
        # 清除生鲜购物车
        # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
        # 获取所有一级普通分类,不包括特殊分类
        category_list = ApiCatalogues().catalogue_home(headers=ec_signup_header)["object"]["category_list"]
        # 根据获取到的一级分类，在访问对应的普通分类
        # filtered_nums = [category["num"] for category in category_list if category["num"] != "global"]
        for category in category_list:
            if category["num"] != "global":
                # 访问普通分类
                SearchByCatalogueContent().search_by_catalogue_content(headers=ec_signup_header,
                                                                       filter_sub_category=category["num"])
                assert self.response["result"] is True
                # 获取分类下的第一个商品
                for product in self.response["object"]["contents"]:
                    if product["type"] == "product":

                        product_id = product["data"]["id"]

                        # 加购特殊分类下的生鲜商品
                        UpdatePreOrderLine().porder_items_v3(headers=ec_signup_header, product_id=product_id,
                                                             date=porder["delivery_pickup_date"],
                                                             item_type=product["data"]["item_type"],
                                                             is_pantry=product["data"]["is_pantry"],
                                                             is_mkpl=product["data"]["is_mkpl"],
                                                             refer_type=product["data"]["biz_type"],
                                                             source="categories-undefined-all")

                        # 判断加购成功
                        assert self.response["result"] is True
                break

        # 调购物车接口

        # 兑换优惠券
        RedeemCoupon().coupons_redeem(headers=ec_signup_header, cart_domain=args[0]["cart"]["cart_domain"],
                                      coupon_code=args[0]["preorder"]["coupon_code"])
        assert self.response["result"] is True

        # # 取消应用优惠券
        # ApplyCoupon().coupons_apply(headers=headers, deal_id=porder["deal_id"],
        #                             cart_domain=args[0]["cart"]["cart_domain"], coupon_code="")
        # 应用优惠券
        ApplyCoupon().coupons_apply(headers=ec_signup_header,cart_domain=args[0]["cart"]["cart_domain"],
                                    coupon_code=args[0]["preorder"]["coupon_code"])
        # 断言
        assert self.response["result"] is True

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip('有问题，待调整')
    def test_coupons_apply_v2(self, *args, ec_signup_header):
        """combine checkout的应用优惠券"""
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_signup_header)["object"]
        # 获取所有一级普通分类,不包括特殊分类
        category_list = ApiCatalogues().catalogue_home(headers=ec_signup_header)["object"]["category_list"]
        # 根据获取到的一级分类，在访问对应的普通分类
        # filtered_nums = [category["num"] for category in category_list if category["num"] != "global"]
        for category in category_list:
            if category["num"] != "global":
                # 访问普通分类
                SearchByCatalogueContent().search_by_catalogue_content(headers=ec_signup_header,
                                                                       filter_sub_category=category["num"])
                assert self.response["result"] is True
                # 获取分类下的第一个商品
                for product in self.response["object"]["contents"]:
                    if product["type"] == "product":

                        product_id = product["data"]["id"]

                        # 加购特殊分类下的生鲜商品
                        UpdatePreOrderLine().porder_items_v3(headers=ec_signup_header, product_id=product_id,
                                                             date=porder["delivery_pickup_date"],
                                                             item_type=product["data"]["item_type"],
                                                             is_pantry=product["data"]["is_pantry"],
                                                             is_mkpl=product["data"]["is_mkpl"],
                                                             refer_type=product["data"]["biz_type"],
                                                             source="categories-undefined-all")

                        # 判断加购成功
                        assert self.response["result"] is True
                    break
        # 兑换优惠券
        RedeemCoupon().coupons_redeem_v2(headers=ec_signup_header)
        assert self.response["result"] is True
        # 应用优惠券
        ApplyCoupon().coupons_apply_v2(headers=ec_signup_header, cart_domain=args[0]["cart"]["cart_domain"],
                                       coupon_code=args[0]["preorder"]["coupon_code"])
        # 断言
        assert self.response["result"] is True


