"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_ricepo_available_coupons.py
@Description    :  
@CreateTime     :  2023/7/22 20:11
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 20:11
"""
import weeeTest

from test_dir.api.ec.ec_so.coupon.query_ricepo_available_coupons import QueryRicepoAvailableCoupons


class TestQueryRicepoAvailableCoupons(weeeTest.TestCase):
    """"""

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_coupons_ricepo(self, ec_login_header):
        QueryRicepoAvailableCoupons().coupons_ricepo(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
