"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_available_coupons.py
@Description    :  
@CreateTime     :  2023/7/21 16:48
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/21 16:48
"""
import weeeTest
from test_dir.api.ec.ec_so.coupon.query_available_coupons import QueryAvailableCoupons


class TestQueryAvailableCoupons(weeeTest.TestCase):
    """查询可用的coupon信息"""

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_coupons_list(self, ec_login_header):
        QueryAvailableCoupons().coupons_list(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product')
    def test_coupons_list_v2(self, *args, ec_login_header):
        QueryAvailableCoupons().coupons_list_v2(ec_login_header, args[0]["cart"]["cart_domain"])
        # 断言
        assert self.response["result"] is True
