"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_get_shipping_free_fee_by_zipcode.py
@Description    :  
@CreateTime     :  2023/7/22 20:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 20:52
"""
import weeeTest
from test_dir.api.ec.ec_so.customer.get_shipping_free_fee_by_zipcode import GetShippingFreeFeeByZipcode


class TestGetShippingFreeFeeByZipcode(weeeTest.TestCase):
    """查询zipcode的运费数据"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_zipcode_shipping_free_fee(self, *args, ec_login_header):
        GetShippingFreeFeeByZipcode().zipcode_shipping_free_fee(ec_login_header, args[0]["preorder"]["zipcode"])
        # 断言
        assert self.response["result"] is True


