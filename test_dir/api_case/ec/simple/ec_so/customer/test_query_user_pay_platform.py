"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_user_pay_platform.py
@Description    :  
@CreateTime     :  2023/7/22 20:25
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 20:25
"""
import weeeTest
from test_dir.api.ec.ec_so.customer.query_user_pay_platform import QueryUserPayPlatform


class TestQueryUserPayPlatform(weeeTest.TestCase):
    """查询用户支付平台"""

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_coupons_apply(self, ec_login_header):
        QueryUserPayPlatform().pay_platform(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

