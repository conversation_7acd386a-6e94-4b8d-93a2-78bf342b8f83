"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_remove_selected_gift_from_cart.py
@Description    :  
@CreateTime     :  2023/7/20 11:32
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 11:32
"""
import weeeTest

from test_dir.api.ec.ec_so.activity.revert_selected_gift_to_cart import RevertSelectedGiftToCart


class TestRevertSelectedGiftToCart(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_remove_selected_gift_from_cart(self, *args, ec_login_header):
        """test_remove_selected_gift_from_cart"""

        RevertSelectedGiftToCart().gift_cart_revent(headers=ec_login_header, product_id=args[0]["order"]["product_id"],
                                                    quantity=args[0]["order"]["quantity"])
        # 断言
        assert self.response["result"] is True

