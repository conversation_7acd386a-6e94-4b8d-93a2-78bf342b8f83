"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_remove_selected_gift_from_cart.py
@Description    :  
@CreateTime     :  2023/7/20 11:32
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 11:32
"""
import weeeTest
from test_dir.api.ec.ec_so.activity.remove_selected_gift_from_cart_v2 import RemoveSelectedGiftFromCartV2


class TestRemoveSelectedGiftFromCartV2(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'tb1', 'dev')
    def test_remove_selected_gift_from_cart(self, *args, ec_login_header):
        """test_remove_selected_gift_from_cart"""
        RemoveSelectedGiftFromCartV2().gift_cart_delete_v2(headers=ec_login_header, product_id=args[0]["cart"]["product_id"],
                                                           quantity=args[0]["cart"]["quantity"])
        # 断言
        assert self.response["result"] is True
