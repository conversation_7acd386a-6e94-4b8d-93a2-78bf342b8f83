"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_delete_address.py
@Description    :  
@CreateTime     :  2023/7/25 9:17
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 9:17
"""
import weeeTest

from test_dir.api.ec.ec_so.address.delete_address import DeleteUserAddress


class TestDeleteAddress(weeeTest.TestCase):
    """对地址进行应用"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_delete_address(self, *args, ec_login_header):
        DeleteUserAddress().delete_address(headers=ec_login_header, address_id=args[0]["address"]["address_id"])
        # 断言
        assert self.response["result"] is True

