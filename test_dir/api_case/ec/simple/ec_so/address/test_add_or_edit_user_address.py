"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_user_address_list.py
@Description    :  
@CreateTime     :  2023/7/20 16:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 16:44
"""
import weeeTest
from test_dir.api.ec.ec_so.address.add_or_edit_user_address import AddOrEditUserAddress


class TestAddOrEditUserAddress(weeeTest.TestCase):
    """新增或者编辑地址信息"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_add_or_edit_user_address(self, *args, ec_login_header):

        AddOrEditUserAddress().so_address(headers=ec_login_header, addr_firstname=args[0]["address"]["addr_firstname"],
                                          addr_lastname=args[0]["address"]["addr_lastname"],
                                          phone=args[0]["address"]["phone"],
                                          email=args[0]["address"]["email"],
                                          addr_zipcode=args[0]["address"]["addr_zipcode"],
                                          address_id=args[0]["address"]["address_id"], comment="comment")
        # 断言
        assert self.response["result"] is True

