"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_user_address_list.py
@Description    :  
@CreateTime     :  2023/7/20 16:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 16:44
"""
import weeeTest
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation


class TestApplyUserAddressInformation(weeeTest.TestCase):
    """对地址进行应用"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    # @weeeTest.mark.skip()
    def test_query_user_address_list(self, *args, ec_login_header):
        ApplyUserAddressInformation().address_apply(headers=ec_login_header, address_id=args[0]["address"]["address_id"],
                                                    type=args[0]["address"]["type"])
        # 断言
        assert self.response["result"] is True

