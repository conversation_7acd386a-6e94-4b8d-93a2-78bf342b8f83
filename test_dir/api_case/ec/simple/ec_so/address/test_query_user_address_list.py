"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_user_address_list.py
@Description    :  
@CreateTime     :  2023/7/20 16:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 16:44
"""
import weeeTest
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList


class TestQueryUserAddressList(weeeTest.TestCase):
    """获取订单地址列表"""

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_user_address_list(self, ec_login_header):
        QueryUserAddressList().address_list(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

