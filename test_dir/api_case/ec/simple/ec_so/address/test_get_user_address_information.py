"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_get_user_address_information.py
@Description    :  
@CreateTime     :  2023/7/20 16:19
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 16:19
"""
import weeeTest
from test_dir.api.ec.ec_so.address.get_user_address_information import GetUserAddressInformation


class TesteGetUserAddressInformation(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip()
    def test_address_info(self, *args, ec_login_header):
        """address_info--有问题，不知道在哪里有用到"""

        print(GetUserAddressInformation().address_info(headers=ec_login_header, address_id=args[0]["address"]["address_id"]))
        # 断言
        # assert self.response["result"] is True
        return self.response


