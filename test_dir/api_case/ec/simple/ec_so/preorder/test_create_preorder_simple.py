# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_so.preorder.create_preorder import CreatePreOrder


class TestCreatePreOrderSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_create_preorder(self, ec_anony_header):
        """ test_create_preorder """
        CreatePreOrder().create_preorder(ec_anony_header, "Safari", "normal")
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'fail_product')
    def test_auto_create_preorder(self, ec_anony_header):
        """ test_auto_create_preorder """
        CreatePreOrder().auto_create_preorder(headers=ec_anony_header)
        assert self.response["result"] is True



