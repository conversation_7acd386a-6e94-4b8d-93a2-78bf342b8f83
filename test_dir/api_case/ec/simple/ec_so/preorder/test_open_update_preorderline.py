# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/7/13 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class TestOpenUpdatePreOrderLine(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_porder_items_v2(self, ec_login_header):
        """ test_porder_items_v2 """

        UpdatePreOrderLine().porder_items_v2(headers=ec_login_header, product_id=84730)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_porder_items_v3(self, ec_login_header):
        """ test_porder_items_v3 """
        UpdatePreOrderLine().porder_items_v3(headers=ec_login_header, product_id=84730)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_remove_cart_v2(self, ec_login_header):
        """ test_remove_cart_v2 """
        UpdatePreOrderLine().remove_cart_v2(headers=ec_login_header, product_id=84730)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_remove_cart_v3(self, ec_login_header):
        """ test_remove_cart_v3 """
        UpdatePreOrderLine().remove_cart_v3(headers=ec_login_header, product_id=84730)
        assert self.response["result"] is True


