# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_so.preorder.get_porder import GetPreOrder

import random


class TestGetPreOrderrSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'tb1')
    def test_get_create_preorder(self, ec_signup_header):
        """ test_get_create_preorder """

        GetPreOrder().get_porder(ec_signup_header, "1")
        assert self.response["result"] is True

