# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_so.preorder.cart_upsell import CartUpsell


class TestCartUpsellSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_cart_upsell(self, ec_login_header):
        """ test_cart_upsell """
        CartUpsell().cart_upsell(headers=ec_login_header)
        assert self.response["result"] is True



