# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from test_dir.api.ec.ec_so.address.add_or_edit_user_address import AddOrEditUserAddress
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode


class TestUpdateZipcodeSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_update_zipcode_v1(self, ec_login_header):
        """ test_update_zipcode_v1 """

        # headers = LoginHeader().signup_header()
        # 查看用户地址
        address_list = QueryUserAddressList().address_list(headers=ec_login_header)
        if address_list["object"] is None:
            # 添加地址
            AddOrEditUserAddress().so_address(headers=ec_login_header, comment='test comment')
        else:
            # 更换地址/zipcode
            UpdateZipcode().update_zipcode_v1(headers=ec_login_header)

        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product')
    def test_update_zipcode_v2(self, ec_login_header):
        """ test_update_zipcode_v2 """

        # headers = LoginHeader().signup_header()
        address_list = QueryUserAddressList().address_list(headers=ec_login_header)
        if address_list["object"] is None:
            AddOrEditUserAddress().so_address(headers=ec_login_header, comment="test comment")

        else:
            UpdateZipcode().update_zipcode_v2(headers=ec_login_header)

        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product')
    def test_update_zipcode_v3(self, ec_login_header):
        """ test_update_zipcode_v3 """

        address_list = QueryUserAddressList().address_list(headers=ec_login_header)
        if address_list["object"] is None:
            AddOrEditUserAddress().so_address(headers=ec_login_header, comment='test comment')

        else:
            UpdateZipcode().update_zipcode_v3(headers=ec_login_header)

        assert self.response["result"] is True


