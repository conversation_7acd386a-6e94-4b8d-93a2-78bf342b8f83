# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
import random
from test_dir.api.ec.ec_so.preorder.query_zipcode_by_region_id import QueryZipcodeByRegionId


class TestQueryZipcodeByRegionIdSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'tb1')
    def test_query_zipcode_by_region_id(self, ec_signup_header):
        """ test_query_zipcode_by_region_id """
        # 还注册请求的，不迁往线上
        QueryZipcodeByRegionId().query_zipcode_by_region_id(headers=ec_signup_header)
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
