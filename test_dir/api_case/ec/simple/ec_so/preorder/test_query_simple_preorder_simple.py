# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestQuerySimplePreOrderSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_simple_preorder_v1(self, ec_login_header):
        """ test_query_simple_preorder_v1 """

        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_simple_preorder_v2(self, ec_login_header):
        """ test_query_simple_preorder_v2 """

        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip('preorder_v3接口有问题，调用报404')
    def test_query_simple_preorder_v3(self, ec_login_header):
        """ preorder_v3接口有问题，调用报404 """
        QuerySimplePreOrder().query_simple_preorder_v3(headers=ec_login_header)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip('preorder_by_user接口有问题，调用报404')
    def test_query_simple_preorder_by_user(self, ec_login_header):
        """ preorder_by_user接口有问题，调用报404 """
        user_id = self.response["object"]["user_id"]
        QuerySimplePreOrder().query_simple_preorder_by_user(headers=ec_login_header, user_id=12491964)
        assert self.response["result"] is True

