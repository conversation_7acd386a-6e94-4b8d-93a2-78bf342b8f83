# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_so.preorder.bind_user import BindUser



class TestBindUserSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_bind_user(self, *args, ec_login_header):
        """ test_bind_user """
        BindUser().bind_user(ec_login_header, args[0]["user"]["user_id"])
        assert self.response["result"] is True

