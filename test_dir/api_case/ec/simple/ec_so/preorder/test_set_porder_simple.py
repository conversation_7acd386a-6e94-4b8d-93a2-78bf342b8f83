# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_so.preorder.set_porder import SetPreOrder



class TestSetPorderSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_set_porder(self, ec_anony_header):
        """ test_set_porder """
        SetPreOrder().set_porder(ec_anony_header, 483606, "2023-07-24", str(10923005))
        assert self.response["result"] is True

