# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest

from test_dir.api.ec.ec_so.preorder.clear_invalid_vendors import ClearInvalidVendors



class TestClearInvalidVendorsSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'tb1', 'dev')
    def test_clear_invalid_vendors(self, ec_signup_header):
        """ test_clear_invalid_vendors """
        ClearInvalidVendors().clear_invalid_vendors(headers=ec_signup_header)
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
