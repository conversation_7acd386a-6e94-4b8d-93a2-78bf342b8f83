# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder


class TestQueryPreOrderSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_preorder_v2(self, ec_login_header):
        """ test_query_preorder_v2 """

        QueryPreOrder().query_preorder_v2(headers=ec_login_header)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_preorder_v3(self, ec_login_header):
        """ test_query_preorder_v3 """

        QueryPreOrder().query_preorder_v3(headers=ec_login_header)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_preorder_v4(self, ec_login_header):
        """ test_query_preorder_v4 """

        QueryPreOrder().query_preorder_v4(headers=ec_login_header)
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_preorder_v5(self, ec_login_header):
        """ test_query_preorder_v5 """
        QueryPreOrder().query_preorder_v5(headers=ec_login_header)
        assert self.response["result"] is True
