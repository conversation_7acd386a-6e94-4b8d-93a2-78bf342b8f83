# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/7/13 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_so.save_for_later.cart_to_save_for_later import CartToSaveForLater



class TestCartToSaveForLaterSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    def test_cart_to_save_for_later(self, *args, ec_login_header):
        """ 从cart移入save_for_later """
        CartToSaveForLater().cart_to_save_for_later(headers=ec_login_header, product_keys=[args[0]["cart"]["product_id"]])
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
