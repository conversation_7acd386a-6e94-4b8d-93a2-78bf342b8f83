# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/7/13 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_so.save_for_later.save_for_later_item_move_to_cart import SaveForLaterItemMoveToCart


class TestSaveForLaterItemMoveToCartSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_save_for_later_item_move_to_cart(self, *args, ec_login_header):
        """ 从save_for_later加入cart """
        SaveForLaterItemMoveToCart().save_for_later_item_move_to_cart(headers=ec_login_header,
                                                                      product_keys=[args[0]["cart"]["product_id"]])
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction')
    def test_save4later_v2(self, ec_login_header):
        """ 加载更多稍后再买商品 """
        SaveForLaterItemMoveToCart().save4later_v2(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True
