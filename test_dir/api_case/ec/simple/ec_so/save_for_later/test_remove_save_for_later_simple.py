# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/7/13 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_so.save_for_later.remove_save_for_later import RemoveSaveForLater



class TestRemoveSaveForLaterSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_remove_save_for_later(self, *args, ec_login_header):
        """ test_remove_save_for_later """
        RemoveSaveForLater().remove_save_for_later(ec_login_header, [args[0]["cart"]["product_id"]])
        # 断言
        assert self.response["result"] is True


