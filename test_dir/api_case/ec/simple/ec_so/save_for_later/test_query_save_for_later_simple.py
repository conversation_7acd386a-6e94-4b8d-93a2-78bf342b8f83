# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/7/13 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_so.save_for_later.query_save_for_later import QuerySaveForLater


class TestQuerySaveForLaterSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_save_for_later(self, ec_login_header):
        """ test_query_save_for_later """
        QuerySaveForLater().query_save_for_later(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

