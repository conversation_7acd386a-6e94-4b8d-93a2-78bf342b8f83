"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_get_valid_delivery_dates.py
@Description    :  
@CreateTime     :  2023/7/22 21:05
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 21:05
"""
import weeeTest
from test_dir.api.ec.ec_so.deliverydates.get_valid_delivery_dates import GetValidDeliveryDates


class TestGetValidDeliveryDates(weeeTest.TestCase):
    """查询可用的配送日期"""

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_so_delivery_date(self, ec_login_header):
        GetValidDeliveryDates().so_delivery_date(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

