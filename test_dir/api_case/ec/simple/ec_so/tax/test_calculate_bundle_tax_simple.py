# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/7/13 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import weeeTest
from test_dir.api.ec.ec_so.tax.calculate_bundle_tax import CalculateBundleTax


class TestCalculateBundleTaxSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_calculate_bundle_tax(self, ec_login_header):
        """ test_calculate_bundle_tax """
        CalculateBundleTax().calculate_bundle_tax(ec_login_header, 98011, 0,"grocery02", "18.88", 102565, "", 3)
        # 断言
        assert self.response["result"] is True


