# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_so.order.update_order_points import UpdateOrderPoints


class TestUpdateOrderPointsSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_update_order_points(self, ec_login_header):
        """ # test_update_order_points """
        UpdateOrderPoints().update_order_points(ec_login_header, "500", "5", "1")
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product')
    def test_bach_update_order_points(self, ec_login_header):
        """ # test_bach_update_order_points """
        UpdateOrderPoints().batch_update_order_points(ec_login_header, "500", "5", "1")
        # 断言
        assert self.response["result"] is True


