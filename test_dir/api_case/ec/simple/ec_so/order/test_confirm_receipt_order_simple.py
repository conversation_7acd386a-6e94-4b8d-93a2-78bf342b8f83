# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order.confirm_receipt_order import ConfirmReceiptOrder
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder


class TestConfirmReceiptOrderSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'tb1', 'dev')
    def test_confirm_receipt_order(self, ec_login_header):
        """ # test_confirm_receipt_order """
        # 获取已发货订单，拿到已收货订单id, 线上目前拿不到
        listall = ListMyOrder().list_my_order_v2(ec_login_header, filter_status="3", filter_date="0")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            ConfirmReceiptOrder().confirm_receipt_order(ec_login_header, order_id)
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有全部订单，请先下单")


