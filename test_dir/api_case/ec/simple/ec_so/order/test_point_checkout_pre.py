"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_point_checkout_pre.py
@Description    :  
@CreateTime     :  2023/11/3 13:27
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/3 13:27
"""
import weeeTest
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout



class TestPointCheckoutPre(weeeTest.TestCase):
    """"""
    @weeeTest.mark.list('Transaction', 'tb1', 'dev')
    def test_point_checkout_pre(self, ec_login_header):
        """ # test_point_checkout_pre """
        PrepareCheckout().point_checkout_pre(headers=ec_login_header,product_type="point")
        # 断言
        assert self.response["result"] is True


