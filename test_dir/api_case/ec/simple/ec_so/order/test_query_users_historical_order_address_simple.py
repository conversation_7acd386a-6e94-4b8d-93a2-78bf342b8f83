# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order.query_users_historical_order_address import QueryUsersHistoricalOrderAddress


class TestQueryUsersHistoricalOrderAddressSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_users_historical_order_address(self, ec_login_header):
        """ # test_query_users_historical_order_address """
        QueryUsersHistoricalOrderAddress().query_users_historical_order_address(headers=ec_login_header)
        if not QueryUsersHistoricalOrderAddress().query_users_historical_order_address(headers=ec_login_header)["object"]:
            log.info("用户不存在历史订单地址，请先下单")
        else:
            log.info("用户存在历史订单地址，请先下单")
        # 断言
        assert self.response["result"] is True


