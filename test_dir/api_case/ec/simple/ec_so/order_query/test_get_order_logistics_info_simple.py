# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import  log

from test_dir.api.ec.ec_so.order_query.track_order import TrackOrder
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder


class TestGetOrderLogisticsInfoSimple(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_order_logistics_info(self, *args, ec_login_header):
        """ # test_get_order_logistics_info """
        # 获取已发货订单，拿到order_id
        list = ListMyOrder().list_my_order_v1(ec_login_header, "3")
        if list["object"]["total"] > 0:
            order_id = list["object"]["myOrders"][0]["id"]
            if order_id is None:
                order_id = args[0]["listmyorder"]["order_id"]
                TrackOrder().track_order(ec_login_header, str(order_id))
            else:
                TrackOrder().track_order(ec_login_header, str(order_id))
            log.info(self.response["object"]["message"])
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")
            # 断言
            assert self.response["result"] is True


