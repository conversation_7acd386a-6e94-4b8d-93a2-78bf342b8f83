# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_so.order_query.get_me_order_num import GetMeOrderNum


class TestGetMeOrderNumSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_me_order_num(self, ec_login_header):
        """ # test_get_me_order_num """
        GetMeOrderNum().get_me_order_num(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product')
    def test_get_me_ondemand_order_num(self, ec_login_header):
        """ # test_get_me_ondemand_order_num """
        GetMeOrderNum().get_me_ondemand_order_num(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True


