# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from test_dir.api.ec.ec_so.order_query.get_cancel_order_reason import GetCancelOrderReason



class TestGetCancelOrderReasonSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_cancel_order_reason(self, ec_login_header):
        """ # test_get_cancel_order_reason """
        GetCancelOrderReason().get_cancel_order_reason(headers=ec_login_header)
        # 断言
        assert self.response["object"] != []  # 取消订单原因不能为空
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
