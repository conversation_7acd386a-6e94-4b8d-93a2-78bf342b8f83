# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import  log

from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api.ec.ec_so.order_query.order_success_page_information import OrderSuccessPageInformation


class TestOrderSuccessPageInformationSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction', 'dev')
    def test_order_success_page_information_v1(self, ec_login_header):
        """ # test_order_success_page_information_v1 """
        # 获取全部订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(ec_login_header, "all")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            OrderSuccessPageInformation().order_success_page_information_v1(ec_login_header, str(order_id))
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")
        return self.response

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_order_success_page_information_v2(self, ec_login_header):
        """ # test_order_success_page_information_v2 """
        # 获取全部订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(ec_login_header, "all")
        if listall["object"]["total"] > 0:
            checkout_id = listall["object"]["myOrders"][0]["id"]
            OrderSuccessPageInformation().order_success_page_information_v2(ec_login_header, str(checkout_id))
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")
        return self.response


