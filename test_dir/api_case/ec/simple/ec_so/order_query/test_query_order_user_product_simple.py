# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api.ec.ec_so.order_query.query_order_user_product import QueryOrderUserProduct


class TestQueryOrderUserProductSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_order_user_product(self, ec_login_header):
        """ # test_query_order_user_product """
        # 获取全部订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(headers=ec_login_header, filter_status="all", filter_date=4)
        if listall["object"]["total"] > 0:
            deal_id = listall["object"]["myOrders"][0]["deal_id"]
            product_id = listall["object"]["myOrders"][0]["products"][0]["product_id"]
            QueryOrderUserProduct().query_order_user_product(headers=ec_login_header, deal_id=deal_id, product_ids=[product_id])
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")
            assert self.response["result"] is True

