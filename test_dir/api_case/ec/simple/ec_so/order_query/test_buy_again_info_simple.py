# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order_query.buy_again_info import BuyAgainInfo
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder


class TestOrderSuccessPageInformationSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip("这个用例没写完")
    def test_buy_again_info_v1(self, ec_login_header):
        """ # test_buy_again_info_v1 """
        # 获取已取消订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(ec_login_header, "4")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            BuyAgainInfo().buy_again_info_v1(ec_login_header, str(order_id))
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")
        return self.response

    @weeeTest.mark.list('Transaction', 'product-debug')
    def test_buy_again_info_v2(self, ec_login_header):
        """ # test_buy_again_info_v2 """
        # 可能会下单，所以去掉product标签
        # 获取已取消订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(ec_login_header, "4")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            BuyAgainInfo().buy_again_info_v2(ec_login_header, str(order_id))
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")


