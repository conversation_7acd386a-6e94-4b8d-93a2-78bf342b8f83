# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_so.order_query.get_purchase_history_of_users_for_certain_products import \
    GetPurchaseHistoryCertainProducts


class TestGetPurchaseHistoryCertainProductsSimple(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction')
    @weeeTest.mark.skip(reason='接口弃用')
    def test_get_purchase_history_of_users_for_certain_products(self, ec_login_header):
        """ # test_get_payment_url_v1 """

        GetPurchaseHistoryCertainProducts().get_purchase_history_of_users_for_certain_products(headers=ec_login_header)
        print(GetPurchaseHistoryCertainProducts().get_purchase_history_of_users_for_certain_products(headers=ec_login_header))
        # 断言
        assert self.response["result"] is True
        return self.response


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
