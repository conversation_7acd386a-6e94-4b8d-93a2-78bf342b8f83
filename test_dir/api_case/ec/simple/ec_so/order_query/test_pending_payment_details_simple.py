# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api.ec.ec_so.order_query.payloading_page_to_get_order_information import \
    PayLoadingPageToGetOrderInformation


class TestPayLoadingPageToGetOrderInformationSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_payloading_page_to_get_order_information_v1(self, ec_login_header):
        """ # test_payloading_page_to_get_order_information_V1 """
        # 用户点击结算后的页面订单信息，微信支付,扫码页面，暂无微信支付
        listall = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status="all", filter_date="1")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            print(order_id)
            PayLoadingPageToGetOrderInformation().payloading_page_to_get_order_information_v1(headers=ec_login_header,
                                                                                              order_id=order_id)
            print(PayLoadingPageToGetOrderInformation().payloading_page_to_get_order_information_v1(headers=ec_login_header,
                                                                                                    order_id=order_id))
            # 断言
            # assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")

    @weeeTest.mark.list('Transaction', 'product')
    def test_payloading_page_to_get_order_information_v2(self, ec_login_header):
        """ # test_payloading_page_to_get_order_information_v2 """
        # 获取全部订单，拿到订单id
        listall = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status="all", filter_date="1")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            PayLoadingPageToGetOrderInformation().payloading_page_to_get_order_information_v2(headers=ec_login_header,
                                                                                              order_id=str(order_id))
            # 断言
            # assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")


