# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order_query.get_modify_order_info import GetModifyOrderInfo
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder


class TestGetModifyOrderInfoSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_modify_order_info(self, ec_login_header):
        """ # test_get_modify_order_info """
        # 获取全部订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(headers=ec_login_header, filter_status="all")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            GetModifyOrderInfo().get_modify_order_info(headers=ec_login_header, order_id=str(order_id))
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
