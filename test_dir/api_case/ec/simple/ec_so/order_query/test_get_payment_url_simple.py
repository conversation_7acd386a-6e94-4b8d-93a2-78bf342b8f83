# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order_query.get_payment_url import GetPaymentUrl
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder


class TestGetPaymentUrlSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_payment_url_v1(self, *args, ec_login_header):
        """ # deal pay 待支付V1 """
        platform = ec_login_header["Platform"]
        if platform == "pc":
            # 获取待支付订单，拿到order_id
            list = ListMyOrder().list_my_order_v1(headers=ec_login_header, filter_status="1", filter_date="1")
            if list["object"]["total"] > 0:
                order_id = list["object"]["myOrders"][0]["id"]
                if order_id is None:
                    order_id = args[0]["listmyorder"]["order_id"]
                    GetPaymentUrl().get_payment_url_v1(headers=ec_login_header, order_id=order_id)
                else:
                    GetPaymentUrl().get_payment_url_v1(headers=ec_login_header, order_id=order_id)
                # 断言
                assert self.response["result"] is True
            else:
                log.info("没有待支付订单，请先下单")
                # 断言
                assert self.response["result"] is True
        else:
            # 获取待支付订单，拿到order_id
            list = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status="1", filter_date="1")
            if list["object"]["total"] > 0:
                order_id = list["object"]["myOrders"][0]["id"]
                if order_id is None:
                    order_id = args[0]["listmyorder"]["order_id"]
                    GetPaymentUrl().get_payment_url_v2(headers=ec_login_header, order_id=order_id)
                else:
                    GetPaymentUrl().get_payment_url_v2(headers=ec_login_header, order_id=order_id)
                # 断言
                assert self.response["result"] is True
            else:
                log.info("没有待支付订单，请先下单")
                # 断言
                assert self.response["result"] is True


