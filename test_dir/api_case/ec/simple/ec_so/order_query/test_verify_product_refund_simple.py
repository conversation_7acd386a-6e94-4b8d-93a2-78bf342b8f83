# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api.ec.ec_so.order_query.verify_product_refund import VerifyProductRefund



class TestVerifyProductRefundSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'tb1')
    def test_verify_product_refund(self, ec_login_header):
        """ # test_verify_product_refund """
        # todo suqin 报错：Unfortunately the refund period for this order has passed. Please refer to our
        # 获取全部订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(headers=ec_login_header, filter_status="all")
        if listall["object"]["total"] > 0:
            order_product_id = listall["object"]["myOrders"][0]["products"][0]["product_id"]
            VerifyProductRefund().verify_product_refund(headers=ec_login_header, order_product_id=order_product_id)
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
