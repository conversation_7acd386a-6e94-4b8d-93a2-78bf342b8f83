# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_order.py
@Description    :
@CreateTime     :  2023/7/25 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 19:06
"""
import weeeTest

from test_dir.api.ec.ec_so.mkpl_group_buy.query_vendor_group_buy_information import QueryVendorGroupBuyInfo


class TestQueryVendorGroupBuyInfoSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', "product", 'dev')
    def test_query_groupbuy_info(self, *args, ec_charlie_header):
        """
        test_query_groupbuy_info
        """
        QueryVendorGroupBuyInfo().vendor_groupbuy_info(headers=ec_charlie_header, vendor_id=args[0]["seller_info"]["vendor_id"])
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
