# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_order.py
@Description    :
@CreateTime     :  2023/7/25 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 19:06
"""
import weeeTest
from test_dir.api.ec.ec_so.mkpl_group_buy.check_group_shopping_cart import MkplGroupOrderQuery


class TestQueryMkplGroupBuySimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product-skip', 'dev')
    def test_mkpl_group_buy(self, *args, ec_charlie_header):
        """
               #test_mkpl_group_buy
        """
        MkplGroupOrderQuery().mkpl_group_buy(headers=ec_charlie_header)

        print(self.response)

        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
