# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_order.py
@Description    :
@CreateTime     :  2023/7/25 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 19:06
"""
import weeeTest

from test_dir.api.ec.ec_so.mkpl_group_buy.create_group_shopping_cart import CreateGroupOrderCart


class TestCreatGroupCartSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_create_group_order(self, *args, ec_charlie_header):
        """
        test_create_group_order
        """
        CreateGroupOrderCart().create_group_order(headers=ec_charlie_header,
                                                  vendor_id=args[0]["seller_info"]["vendor_id"])


        # 断言
        assert self.response["result"] is True


