# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/24 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/24 17:06
"""
import weeeTest

from test_dir.api.ec.ec_so.seller.query_mkpl_float_cart import MkplFloatCart


class TestMkplFloatCartSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_mkpl_float_cart(self, *args, ec_charlie_header):
        """
                #test_seller_cart 测试商家悬浮购物车信息
        """
        MkplFloatCart().mkpl_float_cart(ec_charlie_header, args[0]["seller_info"]["vendor_id"],
                                        args[0]["seller_info"]["show_product_detail"])

        print(self.response)
        # 断言
        assert self.response["result"] is True


