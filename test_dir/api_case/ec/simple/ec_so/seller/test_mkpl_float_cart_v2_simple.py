# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/24 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/24 17:06
"""
import weeeTest

from test_dir.api.ec.ec_so.seller.query_mkpl_float_cart_v2 import MkplFloatCartV2

class TestMkplFloatCartSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_mkpl_float_cart_v2(self, *args, ec_charlie_header):
        """
                #test_seller_cart 测试商家悬浮购物车信息
        """

        MkplFloatCartV2().mkpl_float_cart_v2(headers=ec_charlie_header)
        # 断言
        assert self.response["result"] is True
